// Quick test to verify token validation fix
import { validateTokenFormat } from './functions/src/middleware/input-validators.js';

// Test cases
const testCases = [
  {
    name: "32-character Payrix payment token",
    token: "23fe67bd2166334cfd7b80ab14ca62e4",
    expected: true
  },
  {
    name: "64-character integration token", 
    token: "23fe67bd2166334cfd7b80ab14ca62e423fe67bd2166334cfd7b80ab14ca62e4",
    expected: true
  },
  {
    name: "Invalid short token",
    token: "23fe67bd",
    expected: false
  },
  {
    name: "Invalid long token",
    token: "23fe67bd2166334cfd7b80ab14ca62e423fe67bd2166334cfd7b80ab14ca62e423fe67bd",
    expected: false
  },
  {
    name: "Non-hex characters",
    token: "23fe67bd2166334cfd7b80ab14ca62g4",
    expected: false
  }
];

console.log("Testing token validation...\n");

testCases.forEach(testCase => {
  const result = validateTokenFormat(testCase.token);
  const passed = result.isValid === testCase.expected;
  
  console.log(`${passed ? '✅' : '❌'} ${testCase.name}`);
  console.log(`   Token: ${testCase.token}`);
  console.log(`   Expected: ${testCase.expected}, Got: ${result.isValid}`);
  if (!result.isValid) {
    console.log(`   Error: ${result.message}`);
  }
  console.log();
});
