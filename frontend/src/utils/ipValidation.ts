export interface IpValidationResult {
  isValid: boolean;
  type: "ipv4" | "ipv6" | "invalid";
  error?: string;
}

export const validateIpAddress = (ip: string): IpValidationResult => {
  if (!ip || typeof ip !== "string") {
    return {
      isValid: false,
      type: "invalid",
      error: "IP address is required and must be a string",
    };
  }

  const trimmedIp = ip.trim();

  if (trimmedIp.length === 0) {
    return {
      isValid: false,
      type: "invalid",
      error: "IP address cannot be empty",
    };
  }

  // Check for IPv4
  if (isValidIpv4(trimmedIp)) {
    return {
      isValid: true,
      type: "ipv4",
    };
  }

  // Check for IPv6
  if (isValidIpv6(trimmedIp)) {
    return {
      isValid: true,
      type: "ipv6",
    };
  }

  return {
    isValid: false,
    type: "invalid",
    error: "Invalid IP address format. Must be a valid IPv4 or IPv6 address.",
  };
};

export const isValidIpv4 = (ip: string): boolean => {
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

  if (!ipv4Regex.test(ip)) {
    return false;
  }

  // Additional validation for edge cases
  const parts = ip.split(".");
  return parts.every((part) => {
    const num = parseInt(part, 10);
    return num >= 0 && num <= 255 && part === num.toString();
  });
};

export const isValidIpv6 = (ip: string): boolean => {
  // Comprehensive IPv6 validation
  const ipv6Patterns = [
    // Full form: 8 groups of 4 hex digits
    /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/,
    // Compressed form with ::
    /^(?:[0-9a-fA-F]{1,4}:)*::(?:[0-9a-fA-F]{1,4}:)*[0-9a-fA-F]{1,4}$/,
    // Leading :: (e.g., ::1)
    /^::(?:[0-9a-fA-F]{1,4}:)*[0-9a-fA-F]{1,4}$/,
    // Trailing :: (e.g., 2001:db8::)
    /^(?:[0-9a-fA-F]{1,4}:)+::$/,
    // Just :: (all zeros)
    /^::$/,
    // Loopback
    /^::1$/,
  ];

  return ipv6Patterns.some((pattern) => pattern.test(ip));
};

export const isPrivateIp = (ip: string): boolean => {
  const validation = validateIpAddress(ip);

  if (!validation.isValid || validation.type !== "ipv4") {
    return false;
  }

  const parts = ip.split(".").map((part) => parseInt(part, 10));
  const [a, b] = parts;

  // Private IPv4 ranges:
  // 10.0.0.0 - ************** (10.0.0.0/8)
  // ********** - ************** (**********/12)
  // *********** - *************** (***********/16)
  // ********* - *************** (loopback)

  return a === 10 || (a === 172 && b >= 16 && b <= 31) || (a === 192 && b === 168) || a === 127;
};

export const isLoopbackIp = (ip: string): boolean => {
  const validation = validateIpAddress(ip);

  if (!validation.isValid) {
    return false;
  }

  if (validation.type === "ipv4") {
    return ip.startsWith("127.");
  }

  if (validation.type === "ipv6") {
    return ip === "::1";
  }

  return false;
};

export const normalizeIpAddress = (ip: string): string => {
  const validation = validateIpAddress(ip);

  if (!validation.isValid) {
    return ip;
  }

  if (validation.type === "ipv4") {
    // Normalize IPv4 by removing leading zeros
    return ip
      .split(".")
      .map((part) => parseInt(part, 10).toString())
      .join(".");
  }

  if (validation.type === "ipv6") {
    // Basic IPv6 normalization (could be more comprehensive)
    return ip.toLowerCase();
  }

  return ip;
};

export const getIpAddressInfo = (ip: string) => {
  const validation = validateIpAddress(ip);

  return {
    ...validation,
    isPrivate: validation.isValid ? isPrivateIp(ip) : false,
    isLoopback: validation.isValid ? isLoopbackIp(ip) : false,
    normalized: validation.isValid ? normalizeIpAddress(ip) : ip,
  };
};

export const formatIpValidationError = (result: IpValidationResult): string => {
  if (result.isValid) {
    return "";
  }

  return result.error || "Invalid IP address format";
};
