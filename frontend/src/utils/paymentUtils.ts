import type { BillingAddress, PaymentInfo } from "../types/payment";

export const formatCurrency = (amount: number, currency = "USD"): string => {
  const formatted = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
  }).format(amount / 100);
  return `${formatted} ${currency}`;
};

export const formatDate = (date = new Date()): string => {
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

export const generateReferenceNumber = (prefix = "TXN"): string => {
  return `${prefix}-${Math.floor(Math.random() * 10000)}`;
};

export const generateOrderNumber = (prefix = "ORD"): string => {
  return `${prefix}-${Math.floor(Math.random() * 10000)}`;
};

export const calculateSubtotal = (amount: number, taxAmount = 0): number => {
  return amount - taxAmount;
};

export const validateBillingAddress = (billingAddress: BillingAddress, termsAccepted: boolean): boolean => {
  return (
    billingAddress.firstName.trim() !== "" &&
    billingAddress.lastName.trim() !== "" &&
    billingAddress.email.trim() !== "" &&
    billingAddress.line1.trim() !== "" &&
    billingAddress.city.trim() !== "" &&
    billingAddress.state.trim() !== "" &&
    billingAddress.zip.trim() !== "" &&
    termsAccepted
  );
};

export const getDisplayReference = (paymentInfo: PaymentInfo): string => {
  return paymentInfo.orderNumber || generateReferenceNumber();
};

export const getDisplayOrderNumber = (paymentInfo: PaymentInfo): string => {
  return paymentInfo.orderNumber || generateOrderNumber();
};

export const configureIframeBodyStyles = (): void => {
  document.body.style.margin = "0";
  document.body.style.padding = "0";
  document.body.style.backgroundColor = "#f8fafc";
  document.body.style.fontFamily = "Inter, system-ui, -apple-system, sans-serif";
};

export const configureIframeViewport = (): void => {
  const viewport = document.querySelector('meta[name="viewport"]');
  if (viewport) {
    viewport.setAttribute("content", "width=device-width, initial-scale=1.0, user-scalable=no");
  }
};

export const formatSuccessMessage = (response: unknown, type: string, data?: unknown) => {
  return {
    type,
    data: data || response,
  };
};

export const formatErrorMessage = (error: unknown, type: string) => {
  const errorMessage = error instanceof Error ? error.message : (error as { message?: string })?.message || "An error occurred";

  return {
    type,
    error: errorMessage,
    details: error,
  };
};
