import type { ReactNode } from "react";
// import PaymentHeader from "../payments/PaymentHeader";
import { PaymentFooter } from "../payments/iframe/PaymentFooter";
import { PrimaryColor, SecondaryColor } from "../../constants/colors";

interface PaymentLayoutProps {
  children: ReactNode;
}

export const PaymentLayout = ({ children }: PaymentLayoutProps) => {
  return (
    <div className="flex flex-col h-screen w-full bg-slate-50">
      <div className="flex flex-col min-h-[64rem] w-[90%] mx-auto px-5 py-5">
        {/* <PaymentHeader /> */}

        <div className="flex-1 flex flex-col w-full">
          <div className="flex-1 bg-white rounded-xl shadow-sm transition-shadow duration-300 overflow-hidden mb-4 sm:mb-6 flex flex-col">
            {/* Top bar for style */}
            <div
              className="h-2 flex-shrink-0"
              style={{
                background: `linear-gradient(to right, ${PrimaryColor.hex}, ${SecondaryColor.hex})`,
              }}
            ></div>

            {/* Content area */}
            <div className="flex-1 flex flex-col p-3 sm:p-4 md:p-6">
              <h1 className="text-lg sm:text-xl md:text-2xl font-semibold text-center mb-4 sm:mb-6 flex-shrink-0" style={{ color: PrimaryColor.hex }}>
                Secure Checkout
              </h1>

              {/* Content area */}
              <div className="flex-1 overflow-auto">{children}</div>
            </div>
          </div>
        </div>

        {/* Footer Area */}
        <PaymentFooter />
      </div>
    </div>
  );
};
