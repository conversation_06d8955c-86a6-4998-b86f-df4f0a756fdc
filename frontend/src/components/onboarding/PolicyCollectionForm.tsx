import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store";
import { updateFormData, nextStep, prevStep } from "../../redux/slices/onboardingSlice";
import { Form<PERSON>ontainer, FormHeader, FormActions } from "./components";
import type { CompliancePolicies, URLOnlyPolicy } from "../../types/payment";

const PolicyCollectionForm = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const defaultPolicy = (type: URLOnlyPolicy["type"], title: string): URLOnlyPolicy => ({
    type,
    title,
    content: "", // Optional for URL-only policy
    url: "", // Required but starts empty
    version: "1.0",
    lastUpdated: new Date().toISOString().split("T")[0],
  });

  const [policies, setPolicies] = useState<CompliancePolicies>(
    formData.compliancePolicies || {
      returnRefundPolicy: defaultPolicy("return_refund", "Return and Refund Policy"),
      privacyPolicy: defaultPolicy("privacy", "Consumer Data Privacy Policy"),
      securityPolicy: defaultPolicy("security", "Secure Checkout Policy"),
      termsAndConditions: defaultPolicy("terms", "Terms and Conditions"),
      deliveryPolicy: defaultPolicy("delivery", "Delivery Policy"),
    }
  );

  const validatePolicies = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validate required policies per Payrix e-commerce disclosure requirements
    const requiredPolicies = [
      { key: "returnRefundPolicy", name: "Return & Refund Policy" },
      { key: "privacyPolicy", name: "Consumer Data Privacy Policy" },
      { key: "securityPolicy", name: "Secure Checkout Policy" },
      { key: "termsAndConditions", name: "Terms and Conditions" },
    ];

    requiredPolicies.forEach(({ key, name }) => {
      const policy = policies[key as keyof CompliancePolicies];

      if (!policy) {
        newErrors[`${key}.missing`] = `${name} is missing`;
        return;
      }

      if (!policy.type) {
        newErrors[`${key}.type`] = `${name} type is missing`;
      }

      if (!policy.title || policy.title.length < 1) {
        newErrors[`${key}.title`] = `${name} title is required by Payrix compliance`;
      }

      if (!policy.url || policy.url.length < 1) {
        newErrors[`${key}.url`] = `${name} URL is required by Payrix compliance`;
      }
    });

    // Validate delivery policy (optional - only validate if user starts filling it)
    if (policies.deliveryPolicy?.title || policies.deliveryPolicy?.url) {
      if (policies.deliveryPolicy.title && (!policies.deliveryPolicy.url || policies.deliveryPolicy.url.length < 1)) {
        newErrors["deliveryPolicy.url"] = "Delivery policy URL is required when title is provided";
      }
      if (policies.deliveryPolicy.url && (!policies.deliveryPolicy.title || policies.deliveryPolicy.title.length < 1)) {
        newErrors["deliveryPolicy.title"] = "Delivery policy title is required when URL is provided";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handlePolicyChange = (policyKey: keyof CompliancePolicies, field: keyof URLOnlyPolicy, value: string) => {
    setPolicies((prev) => {
      const currentPolicy = prev[policyKey];
      if (!currentPolicy) {
        return prev;
      }

      return {
        ...prev,
        [policyKey]: {
          ...currentPolicy,
          [field]: value,
        },
      };
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validatePolicies()) {
      // Prepare compliance policies for submission
      // Only include deliveryPolicy if it has both title and URL
      const compliancePoliciesForSubmission: CompliancePolicies = {
        returnRefundPolicy: policies.returnRefundPolicy,
        privacyPolicy: policies.privacyPolicy,
        securityPolicy: policies.securityPolicy,
        termsAndConditions: policies.termsAndConditions,
      };

      // Only include deliveryPolicy if it's properly filled out
      if (policies.deliveryPolicy?.title && policies.deliveryPolicy?.url) {
        compliancePoliciesForSubmission.deliveryPolicy = policies.deliveryPolicy;
      }

      dispatch(updateFormData({ compliancePolicies: compliancePoliciesForSubmission }));
      dispatch(nextStep());
    }
  };

  const handleBack = () => {
    dispatch(prevStep());
  };

  const renderPolicyField = (policyKey: keyof CompliancePolicies, label: string, required: boolean = true, description?: string) => {
    const policy = policies[policyKey];
    if (!policy) return null;

    return (
      <div className="mb-6 p-4 border border-gray-200 rounded-lg">
        <h4 className="text-md font-medium text-gray-900 mb-3">
          {label} {required ? <span className="text-red-500">*</span> : <span className="text-gray-500">(Optional)</span>}
          <span className="ml-2 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">URL-only policy</span>
          {required && <span className="ml-2 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded">Payrix Required</span>}
        </h4>

        {description && <p className="text-sm text-gray-600 mb-3">{description}</p>}

        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Policy Title {required && <span className="text-red-500">*</span>}</label>
            <input
              type="text"
              value={policy.title}
              onChange={(e) => handlePolicyChange(policyKey, "title", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={`Enter ${label.toLowerCase()} title`}
            />
            {errors[`${policyKey}.title`] && <p className="text-red-500 text-xs mt-1">{errors[`${policyKey}.title`]}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Policy URL {required && <span className="text-red-500">*</span>}</label>
            <input
              type="url"
              value={policy.url || ""}
              onChange={(e) => handlePolicyChange(policyKey, "url", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={`https://yourwebsite.com/${label.toLowerCase().replace(/\s+/g, "-")}`}
            />
            {errors[`${policyKey}.url`] && <p className="text-red-500 text-xs mt-1">{errors[`${policyKey}.url`]}</p>}
          </div>

          <div className={`border rounded-lg p-3 ${required ? "bg-orange-50 border-orange-200" : "bg-blue-50 border-blue-200"}`}>
            <p className={`text-xs ${required ? "text-orange-800" : "text-blue-800"}`}>
              <strong>{required ? "Payrix Requirement:" : "Note:"}</strong>{" "}
              {required
                ? "This policy is required by Payrix e-commerce disclosure requirements for all merchants processing online transactions."
                : "This policy is optional but recommended for merchants selling physical goods."}
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <FormContainer>
      <FormHeader
        title="Payrix E-Commerce Compliance Policies"
        subtitle="Configure required and optional policies per Payrix e-commerce disclosure requirements"
      />

      <form onSubmit={handleSubmit} className="px-8 py-8">
        <div className="mb-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h4 className="text-sm font-medium text-blue-900 mb-2">E-commerce Compliance Requirements</h4>
            <div className="text-xs text-blue-800 space-y-1">
              <p>
                <strong>Payrix E-Commerce Disclosure Requirements:</strong>
              </p>
              <p>
                • <strong>Return & Refund Policy:</strong> Required by Visa, MasterCard, AMEX, and Discover
              </p>
              <p>
                • <strong>Consumer Data Privacy Policy:</strong> Required for all e-commerce transactions
              </p>
              <p>
                • <strong>Secure Checkout Policy:</strong> Required for payment cardholder data security
              </p>
              <p>
                • <strong>Terms & Conditions:</strong> Required legal agreement for all purchases
              </p>
              <p>
                • <strong>Delivery Policy:</strong> Optional - only needed for physical goods delivery
              </p>
            </div>
          </div>
        </div>

        {renderPolicyField(
          "returnRefundPolicy",
          "Return & Refund Policy",
          true,
          "Required by Payrix: Must specify your refund policy, even if no refunds are accepted."
        )}
        {renderPolicyField(
          "privacyPolicy",
          "Consumer Data Privacy Policy",
          true,
          "Required by Payrix: Must disclose how you gather, use, and manage customer data."
        )}
        {renderPolicyField(
          "securityPolicy",
          "Secure Checkout Policy",
          true,
          "Required by Payrix: Must list security capabilities for payment cardholder data transmission."
        )}
        {renderPolicyField(
          "termsAndConditions",
          "Terms & Conditions",
          true,
          "Required by Payrix: Must list all legal terms consumers agree to when purchasing."
        )}
        {renderPolicyField("deliveryPolicy", "Delivery Policy", false, "Optional: Only required if you sell physical goods that require delivery.")}

        <FormActions submitText="Continue to Review" onCancel={handleBack} showCancel={true} cancelText="Back" />
      </form>
    </FormContainer>
  );
};

export default PolicyCollectionForm;
