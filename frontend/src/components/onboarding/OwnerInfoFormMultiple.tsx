import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../redux/store.ts";
import { prevStep } from "../../redux/slices/onboardingSlice.ts";
import { FinCenNotice, OwnershipSummary, OwnerFormSection, AccountCreationSection } from "./sections";
import { Member, DEFAULT_MEMBER } from "./constants/ownerConstants";
import { useOwnerFormHandlers } from "./hooks/useOwnerFormHandlers";
import { useAccountFieldValidation } from "./hooks/useAccountFieldValidation";
import { ErrorSummary } from "./components/ErrorSummary";
import { OwnerInfoFormLayout } from "./components/OwnerInfoFormLayout";
import { OwnerManagementSection } from "./components/OwnerManagementSection";
import { OwnerInfoFormNavigation } from "./components/OwnerInfoFormNavigation";

const OwnerInfoFormMultiple = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);

  const members = (formData.merchant?.members as Member[]) || [DEFAULT_MEMBER];

  const { errors, setErrors, handleSubmit, handleChange, addOwner, handlePrimaryChange, removeOwner } = useOwnerFormHandlers(formData, members);

  const { handleAccountFieldChange } = useAccountFieldValidation(formData, errors, setErrors);

  return (
    <OwnerInfoFormLayout>
      {/* FINCEN Language */}
      <div className="px-8 pt-6">
        <FinCenNotice />
      </div>

      <form onSubmit={handleSubmit} className="px-8 pb-8">
        <ErrorSummary errors={errors} />
        {/* Total Ownership Display */}
        <OwnershipSummary members={members} />

        {/* Multiple Owners Question */}
        <OwnerManagementSection members={members} onAddOwner={addOwner} />

        {/* Owner Forms */}
        {members.map((member, index) => (
          <OwnerFormSection
            key={index}
            member={member}
            index={index}
            canRemove={members.length > 1}
            onFieldChange={handleChange}
            onRemove={removeOwner}
            onPrimaryChange={handlePrimaryChange}
            errors={errors}
          />
        ))}

        {/* Account Creation */}
        <AccountCreationSection
          username={formData.username || ""}
          password={formData.password || ""}
          confirmPassword={formData.confirmPassword || ""}
          onFieldChange={handleAccountFieldChange}
          errors={{
            username: errors.username,
            password: errors.password,
            confirmPassword: errors.confirmPassword,
          }}
        />

        {/* Navigation Buttons */}
        <OwnerInfoFormNavigation onPrevious={() => dispatch(prevStep())} />
      </form>
    </OwnerInfoFormLayout>
  );
};

export default OwnerInfoFormMultiple;
