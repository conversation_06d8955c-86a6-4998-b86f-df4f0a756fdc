import { useDispatch } from "react-redux";
import { updateFormData } from "../../../redux/slices/onboardingSlice";

interface FormData {
  password?: string;
  confirmPassword?: string;
}

export const useAccountFieldValidation = (
  formData: FormData,
  errors: Record<string, string>,
  setErrors: (errors: Record<string, string>) => void
) => {
  const dispatch = useDispatch();

  // handleAccountFieldChange to validate and update account creation fields
  const handleAccountFieldChange = (field: string, value: string | boolean) => {
    dispatch(updateFormData({ [field]: value, createAccount: true }));

    if (typeof value === "string") {
      const newErrors = { ...errors };

      // username validation with format and requirement checks
      if (field === "username") {
        if (value) {
          if (!/^[a-z0-9]{3,50}$/.test(value)) {
            newErrors.username = "Username must be 3-50 characters and contain only lowercase letters and numbers";
          } else if (!/(?=.*\d)/.test(value)) {
            newErrors.username = "Username must include at least one number";
          } else {
            delete newErrors.username;
          }
        }
      }

      // password validation with strength requirements
      if (field === "password") {
        if (value) {
          const passwordErrors = [];
          if (value.length < 8) passwordErrors.push("at least 8 characters");
          if (!/(?=.*[a-z])/.test(value)) passwordErrors.push("one lowercase letter");
          if (!/(?=.*[A-Z])/.test(value)) passwordErrors.push("one uppercase letter");
          if (!/(?=.*\d)/.test(value)) passwordErrors.push("one number");
          if (!/(?=.*[@$!%*?&])/.test(value)) passwordErrors.push("one special character (@$!%*?&)");

          if (passwordErrors.length > 0) {
            newErrors.password = `Password must include: ${passwordErrors.join(", ")}`;
          } else {
            delete newErrors.password;
          }
        }

        // check password match when password changes
        if (formData.confirmPassword && value !== formData.confirmPassword) {
          newErrors.confirmPassword = "Passwords do not match";
        } else if (formData.confirmPassword && value === formData.confirmPassword) {
          delete newErrors.confirmPassword;
        }
      }

      // confirmPassword validation to ensure passwords match
      if (field === "confirmPassword") {
        if (value && formData.password && value !== formData.password) {
          newErrors.confirmPassword = "Passwords do not match";
        } else {
          delete newErrors.confirmPassword;
        }
      }

      setErrors(newErrors);
    }
  };

  return { handleAccountFieldChange };
};
