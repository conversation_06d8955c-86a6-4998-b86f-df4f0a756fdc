import { useState } from "react";
import { type ComplianceState, type ComplianceValidationErrors } from "../utils/reviewValidation";

interface UseComplianceStateReturn {
  complianceState: ComplianceState;
  handleComplianceChange: (field: keyof ComplianceState, value: boolean) => void;
  setValidationErrors: React.Dispatch<React.SetStateAction<ComplianceValidationErrors>>;
}

interface UseComplianceStateProps {
  validationErrors: ComplianceValidationErrors;
  setValidationErrors: React.Dispatch<React.SetStateAction<ComplianceValidationErrors>>;
}

export const useComplianceState = ({ validationErrors, setValidationErrors }: UseComplianceStateProps): UseComplianceStateReturn => {
  const [complianceState, setComplianceState] = useState<ComplianceState>({
    visaDisclosure: false,
    payrixTerms: false,
    bankDisclosures: false,
    amexTerms: false,
    attestationStatement: false,
    tcAttestation: false, // Syncs with attestationStatement
  });

  const handleComplianceChange = (field: keyof ComplianceState, value: boolean) => {
    setComplianceState((prev) => ({
      ...prev,
      [field]: value,
      // Sync tcAttestation with attestationStatement
      ...(field === "attestationStatement" ? { tcAttestation: value } : {}),
    }));

    // Clear validation error when user checks the box
    if (value && validationErrors[field]) {
      setValidationErrors((prev: ComplianceValidationErrors) => ({ ...prev, [field]: undefined }));
    }
  };

  return {
    complianceState,
    handleComplianceChange,
    setValidationErrors,
  };
};