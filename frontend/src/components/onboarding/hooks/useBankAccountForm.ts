import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { type RootState } from "../../../redux/store";
import { updateFormData } from "../../../redux/slices/onboardingSlice";
import { toast } from "sonner";
import { validateBankAccount, type BankAccountValidationErrors } from "../utils/validation";
import { VERIFICATION_METHODS, type VerificationMethod } from "../constants/bankAccountConstants";
import { createNote, createNoteDocument } from "../../../services/merchants/api";
import { PlaidAccountData } from "../../../hooks/usePlaidLink";

export const useBankAccountForm = () => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);

  const [errors, setErrors] = useState<BankAccountValidationErrors>({});
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [verificationMethod, setVerificationMethod] = useState<VerificationMethod>(VERIFICATION_METHODS.MANUAL);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [uploadPreview, setUploadPreview] = useState<string | null>(null);
  const [plaidAccountData, setPlaidAccountData] = useState<PlaidAccountData | null>(null);

  const isSoleProprietor = formData.type === 1;
  const account = formData.accounts?.[0] || {
    primary: 1,
    account: {
      method: isSoleProprietor ? 8 : 10,
      number: "",
      routing: "",
    },
  };

  // handlePlaidSuccess to process successful Plaid account connection
  const handlePlaidSuccess = (accountData: PlaidAccountData) => {
    setPlaidAccountData(accountData);
    toast.success("Bank account connected successfully!", {
      description: `Connected ${accountData.institutionName} account ending in ${accountData.accountMask}`,
    });
  };

  // validateForm to validate bank account form data and show errors
  const validateForm = () => {
    const validationErrors = validateBankAccount(account, termsAccepted, verificationMethod, uploadedFile, plaidAccountData);
    setErrors(validationErrors);

    if (Object.keys(validationErrors).length > 0) {
      toast.error("Please fix the following errors:", {
        description: Object.values(validationErrors).join(", "),
      });
      return false;
    }
    return true;
  };

  // handleChange to update bank account form fields
  const handleChange = (field: string, value: string | number) => {
    const updatedAccount = field.startsWith("account.")
      ? {
          ...account,
          currency: "USD",
          account: { ...account.account, [field.replace("account.", "")]: value },
        }
      : { ...account, currency: "USD", [field]: value };
    dispatch(updateFormData({ accounts: [updatedAccount] }));
  };

  // handleFileChange to update uploaded file and clear related errors
  const handleFileChange = (file: File | null) => {
    setUploadedFile(file);
    if (errors.voidCheck && file) {
      setErrors({ ...errors, voidCheck: undefined });
    }
  };

  // handlePreviewChange to update file preview URL
  const handlePreviewChange = (preview: string | null) => {
    setUploadPreview(preview);
  };

  // return hook interface with bank account form state and handlers
  return {
    formData,
    errors,
    setErrors,
    termsAccepted,
    setTermsAccepted,
    verificationMethod,
    setVerificationMethod,
    uploadedFile,
    uploadPreview,
    plaidAccountData,
    isSoleProprietor,
    account,
    handlePlaidSuccess,
    validateForm,
    handleChange,
    handleFileChange,
    handlePreviewChange,
    dispatch,
  };
};

// handleVerificationUpload to upload verification document for manual bank verification
export const handleVerificationUpload = async (
  verificationMethod: VerificationMethod,
  uploadedFile: File | null,
  entityId: string
): Promise<boolean> => {
  if (verificationMethod !== VERIFICATION_METHODS.MANUAL || !uploadedFile) {
    return true;
  }

  try {
    toast.info("Uploading verification document...");

    const noteResponse = await createNote({
      entityId,
      note: "Bank account verification - void check uploaded",
      type: "bank_verification",
    });

    if (!noteResponse.success) {
      throw new Error(noteResponse.message || "Failed to create note");
    }

    const documentResponse = await createNoteDocument({
      noteId: noteResponse.data.noteId,
      file: uploadedFile,
      description: "Void check for bank account verification",
    });

    if (!documentResponse.success) {
      throw new Error(documentResponse.message || "Failed to upload document");
    }

    toast.success("Verification document uploaded successfully");
    return true;
  } catch (error) {
    let errorMessage = "Failed to upload verification document. Please try again.";

    if (error instanceof Error) {
      if (error.message.includes("File must be")) {
        errorMessage = error.message;
      } else if (error.message.includes("File size")) {
        errorMessage = "File size exceeds 10MB limit. Please choose a smaller file.";
      } else if (error.message.includes("network") || error.message.includes("timeout")) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else if (error.message.includes("Invalid token") || error.message.includes("authentication")) {
        errorMessage = "Authentication error. Please refresh the page and try again.";
      }
    }

    toast.error(errorMessage);
    return false;
  }
};
