import { useState, useEffect, useCallback } from "react";
import { validateIpAddress } from "../../../utils/ipValidation";

interface IpDetectionResult {
  ip: string;
  isLoading: boolean;
  error: string | null;
  source: string | null;
  isValid: boolean;
}

interface IpService {
  name: string;
  url: string;
  extractIp: (data: Record<string, unknown>) => string;
}

const IP_SERVICES: IpService[] = [
  {
    name: "ipify",
    url: "https://api.ipify.org?format=json",
    extractIp: (data) => String(data.ip || ""),
  },
  {
    name: "ipapi",
    url: "https://ipapi.co/json/",
    extractIp: (data) => String(data.ip || ""),
  },
  {
    name: "httpbin",
    url: "https://httpbin.org/ip",
    extractIp: (data) => String(data.origin || ""),
  },
];

export const useClientIp = (): IpDetectionResult => {
  const [result, setResult] = useState<IpDetectionResult>({
    ip: "127.0.0.1",
    isLoading: true,
    error: null,
    source: null,
    isValid: false,
  });

  const detectIpFromService = useCallback(async (service: IpService): Promise<string | null> => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch(service.url, {
        signal: controller.signal,
        headers: {
          Accept: "application/json",
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const ip = service.extractIp(data);

      if (!ip) {
        throw new Error("No IP address found in response");
      }

      const ipValidation = validateIpAddress(ip);
      if (!ipValidation.isValid) {
        throw new Error(`Invalid IP address format: ${ip} - ${ipValidation.error}`);
      }

      return ip;
    } catch {
      return null;
    }
  }, []);

  const detectClientIp = useCallback(async () => {
    setResult((prev) => ({ ...prev, isLoading: true, error: null }));

    for (const service of IP_SERVICES) {
      const ip = await detectIpFromService(service);

      if (ip) {
        setResult({
          ip,
          isLoading: false,
          error: null,
          source: service.name,
          isValid: true,
        });
        return;
      }
    }

    // All services failed
    setResult({
      ip: "127.0.0.1",
      isLoading: false,
      error: "Failed to detect IP address from all services. Using fallback.",
      source: "fallback",
      isValid: false,
    });
  }, [detectIpFromService]);

  useEffect(() => {
    detectClientIp();
  }, [detectClientIp]);

  return result;
};
