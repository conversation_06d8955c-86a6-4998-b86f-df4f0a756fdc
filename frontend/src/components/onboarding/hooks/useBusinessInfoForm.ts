import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "sonner";
import { nextStep, updateFormData, fillDemoData } from "../../../redux/slices/onboardingSlice";
import { type RootState } from "../../../redux/store";
import { validateBusinessInfoForm, ValidationErrors } from "../utils/validation";
import { BUSINESS_TYPE_CHECKS } from "../constants/businessConstants";
import { determineFieldChangeHandler } from "../utils/businessLogicHandlers";
import { BusinessInfoFormData, UseBusinessInfoFormReturn } from "../types/businessInfoTypes";

export const useBusinessInfoForm = (): UseBusinessInfoFormReturn => {
  const dispatch = useDispatch();
  const { formData } = useSelector((state: RootState) => state.onboarding);
  const [errors, setErrors] = useState<ValidationErrors>({});

  // selectedBusinessType to check if business type is required
  const selectedBusinessType = formData.type;
  // requiresCorporateStructure to check if business type requires corporate structure
  const requiresCorporateStructure = selectedBusinessType && BUSINESS_TYPE_CHECKS.requiresCorporateStructure(selectedBusinessType);

  // useEffect to handle errors
  useEffect(() => {
    if (Object.keys(errors).length > 0) {
      const newErrors = { ...errors };
      let hasChanges = false;

      if (!requiresCorporateStructure && newErrors.dba) {
        delete newErrors.dba;
        hasChanges = true;
      }

      if (formData.merchant?.new === 1 && newErrors.annualCCSales) {
        delete newErrors.annualCCSales;
        hasChanges = true;
      }

      if (hasChanges) {
        setErrors(newErrors);
      }
    }
  }, [selectedBusinessType, requiresCorporateStructure, formData.merchant?.new, errors]);

  // handleSubmit to validate form and dispatch next step
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const validationErrors = validateBusinessInfoForm(formData);
    setErrors(validationErrors);

    if (Object.keys(validationErrors).length === 0) {
      dispatch(nextStep());
    } else {
      const errorCount = Object.keys(validationErrors).length;
      toast.error(`Please fix ${errorCount} validation error${errorCount > 1 ? "s" : ""} before continuing.`);
    }
  };

  // handleChange to update form data
  const handleChange = (field: string, value: string | number) => {
    const updates = determineFieldChangeHandler(field, value, formData);

    if (Object.keys(updates).length > 0) {
      dispatch(updateFormData(updates));
    }
  };

  // handleFillDemoData to fill demo data
  const handleFillDemoData = () => {
    dispatch(fillDemoData());
  };

  return {
    formData: formData as BusinessInfoFormData,
    errors,
    selectedBusinessType,
    requiresCorporateStructure: !!requiresCorporateStructure,
    handleSubmit,
    handleChange,
    handleFillDemoData,
  };
};
