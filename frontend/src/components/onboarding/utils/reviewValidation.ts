import { COMPLIANCE_REQUIREMENTS } from "../constants/reviewConstants";

export interface ComplianceValidationErrors {
  visaDisclosure?: string;
  payrixTerms?: string;
  bankDisclosures?: string;
  amexTerms?: string;
  attestationStatement?: string;
  tcAttestation?: string;
}

export interface ComplianceState {
  visaDisclosure: boolean;
  payrixTerms: boolean;
  bankDisclosures: boolean;
  amexTerms: boolean;
  attestationStatement: boolean;
  tcAttestation: boolean; // Maps to attestationStatement checkbox state
}

export const validateComplianceCheckboxes = (state: ComplianceState): ComplianceValidationErrors => {
  const errors: ComplianceValidationErrors = {};

  if (!state.visaDisclosure) {
    errors.visaDisclosure = COMPLIANCE_REQUIREMENTS.VISA_DISCLOSURE.error;
  }

  if (!state.payrixTerms) {
    errors.payrixTerms = COMPLIANCE_REQUIREMENTS.PAYRIX_TERMS.error;
  }

  if (!state.bankDisclosures) {
    errors.bankDisclosures = COMPLIANCE_REQUIREMENTS.BANK_DISCLOSURES.error;
  }

  if (!state.amexTerms) {
    errors.amexTerms = COMPLIANCE_REQUIREMENTS.AMEX_TERMS.error;
  }

  if (!state.attestationStatement) {
    errors.attestationStatement = COMPLIANCE_REQUIREMENTS.ATTESTATION_STATEMENT.error;
  }

  return errors;
};

export const hasValidationErrors = (errors: ComplianceValidationErrors): boolean => {
  return Object.keys(errors).length > 0;
};
