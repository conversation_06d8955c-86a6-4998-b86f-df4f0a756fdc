import { Member, OWNERSHIP_CONSTANTS } from "../constants/ownerConstants";

export const calculateEqualOwnership = (memberCount: number): number => {
  if (memberCount <= 0) return 0;

  // Calculate equal distribution in basis points
  const equalShare = Math.floor(OWNERSHIP_CONSTANTS.FULL_OWNERSHIP / memberCount);
  return equalShare;
};

export const distributeOwnershipEqually = (memberCount: number): number[] => {
  if (memberCount <= 0) return [];

  const equalShare = Math.floor(OWNERSHIP_CONSTANTS.FULL_OWNERSHIP / memberCount);
  const remainder = OWNERSHIP_CONSTANTS.FULL_OWNERSHIP % memberCount;

  const ownerships: number[] = new Array(memberCount).fill(equalShare);

  // Distribute remainder among first few members to ensure total equals 100%
  for (let i = 0; i < remainder; i++) {
    ownerships[i] += 1;
  }

  return ownerships;
};

export const redistributeOwnership = (members: Member[]): Member[] => {
  if (members.length === 0) return members;

  const ownerships = distributeOwnershipEqually(members.length);

  return members.map((member, index) => ({
    ...member,
    ownership: ownerships[index],
  }));
};

export const ensureSinglePrimaryContact = (members: Member[], primaryIndex?: number): Member[] => {
  if (members.length === 0) return members;

  // If no primary index specified, find current primary or default to first member
  let targetPrimaryIndex = primaryIndex;
  if (targetPrimaryIndex === undefined) {
    targetPrimaryIndex = members.findIndex((member) => member.primary === "1");
    if (targetPrimaryIndex === -1) {
      targetPrimaryIndex = 0; // Default to first member if no primary found
    }
  }

  // Ensure target index is valid
  if (targetPrimaryIndex < 0 || targetPrimaryIndex >= members.length) {
    targetPrimaryIndex = 0;
  }

  return members.map((member, index) => ({
    ...member,
    primary: index === targetPrimaryIndex ? "1" : "0",
  }));
};

export const addMemberWithRedistribution = (members: Member[], newMember: Member): Member[] => {
  const updatedMembers = [...members, { ...newMember, primary: "0" }];
  const redistributed = redistributeOwnership(updatedMembers);
  return ensureSinglePrimaryContact(redistributed);
};

export const removeMemberWithRedistribution = (members: Member[], indexToRemove: number): Member[] => {
  if (indexToRemove < 0 || indexToRemove >= members.length || members.length <= 1) {
    return members;
  }

  const wasRemovedMemberPrimary = members[indexToRemove].primary === "1";
  const updatedMembers = members.filter((_, index) => index !== indexToRemove);
  const redistributed = redistributeOwnership(updatedMembers);

  // If removed member was primary, ensure first remaining member becomes primary
  if (wasRemovedMemberPrimary) {
    return ensureSinglePrimaryContact(redistributed, 0);
  }

  return ensureSinglePrimaryContact(redistributed);
};

export const validateTotalOwnership = (members: Member[]): boolean => {
  const totalOwnership = members.reduce((sum, member) => sum + (member.ownership || 0), 0);
  return totalOwnership === OWNERSHIP_CONSTANTS.FULL_OWNERSHIP;
};

export const validateSinglePrimaryContact = (members: Member[]): boolean => {
  const primaryCount = members.filter((member) => member.primary === "1").length;
  return primaryCount === 1;
};

export const getDisplayPercentage = (ownership: number): number => {
  return ownership / OWNERSHIP_CONSTANTS.PERCENTAGE_DIVISOR;
};

export const formatOwnershipPercentage = (ownership: number, decimals: number = 2): string => {
  const percentage = getDisplayPercentage(ownership);
  return `${percentage.toFixed(decimals)}%`;
};
