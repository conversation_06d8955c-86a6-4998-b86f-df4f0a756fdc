interface OwnerInfoFormNavigationProps {
  onPrevious: () => void;
  submitText?: string;
  submitDisabled?: boolean;
}

export const OwnerInfoFormNavigation = ({ 
  onPrevious, 
  submitText = "Continue to Bank Account",
  submitDisabled = false 
}: OwnerInfoFormNavigationProps) => {
  return (
    <div className="border-t border-gray-200 pt-6 flex justify-between">
      <button
        type="button"
        onClick={onPrevious}
        className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200"
      >
        Previous
      </button>
      <button
        type="submit"
        disabled={submitDisabled}
        className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed"
      >
        {submitText}
      </button>
    </div>
  );
};