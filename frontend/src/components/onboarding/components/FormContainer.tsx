import { ReactNode } from "react";

interface FormContainerProps {
  children: ReactNode;
  className?: string;
}

export const FormContainer = ({ children, className = "" }: FormContainerProps) => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
          {children}
        </div>
      </div>
    </div>
  );
};
