interface FormActionsProps {
  submitText?: string;
  submitDisabled?: boolean;
  showCancel?: boolean;
  onCancel?: () => void;
  cancelText?: string;
  className?: string;
}

export const FormActions = ({
  submitText = "Continue",
  submitDisabled = false,
  showCancel = false,
  onCancel,
  cancelText = "Cancel",
  className = "",
}: FormActionsProps) => {
  return (
    <div className={`border-t border-gray-200 pt-6 flex justify-end gap-4 ${className}`}>
      {showCancel && onCancel && (
        <button
          type="button"
          onClick={onCancel}
          className="bg-gray-200 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-300 focus:ring-4 focus:ring-gray-200 transition-all duration-200"
        >
          {cancelText}
        </button>
      )}
      <button
        type="submit"
        disabled={submitDisabled}
        className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed"
      >
        {submitText}
      </button>
    </div>
  );
};
