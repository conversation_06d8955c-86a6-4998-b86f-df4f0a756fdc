import { Member } from "../constants/ownerConstants";

interface OwnerManagementSectionProps {
  members: Member[];
  onAddOwner: () => void;
}

export const OwnerManagementSection = ({ members, onAddOwner }: OwnerManagementSectionProps) => {
  return (
    <div className="mb-8">
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
        <h3 className="text-sm font-medium text-gray-900 mb-4">
          Does any other principal own 25% or more of the business?
        </h3>
        <div className="flex space-x-4">
          <button
            type="button"
            onClick={onAddOwner}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
          >
            Yes, Add Additional Principal
          </button>
          {members.length === 1 && (
            <span className="text-sm text-gray-600 py-2">
              Currently showing 1 principal
            </span>
          )}
          {members.length > 1 && (
            <span className="text-sm text-gray-600 py-2">
              Currently showing {members.length} principals
            </span>
          )}
        </div>
      </div>
    </div>
  );
};