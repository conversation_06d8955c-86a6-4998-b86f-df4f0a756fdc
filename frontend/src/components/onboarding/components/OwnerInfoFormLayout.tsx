import { ReactNode } from "react";
import { FormContainer } from "./FormContainer";
import { FormHeader } from "./FormHeader";

interface OwnerInfoFormLayoutProps {
  children: ReactNode;
  onFillDemoData?: () => void;
  showDemoButton?: boolean;
}

export const OwnerInfoFormLayout = ({ 
  children, 
  onFillDemoData, 
  showDemoButton = false 
}: OwnerInfoFormLayoutProps) => {
  return (
    <FormContainer>
      <FormHeader
        title="Owner Information"
        subtitle="Details about business owners and principals"
        onFillDemoData={onFillDemoData}
        showDemoButton={showDemoButton}
      />
      {children}
    </FormContainer>
  );
};