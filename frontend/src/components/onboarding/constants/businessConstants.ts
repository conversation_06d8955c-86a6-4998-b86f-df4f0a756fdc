export const BUSINESS_TYPES = [
  { value: 0, label: "Sole Proprietorship" },
  { value: 1, label: "Corporation" },
  { value: 2, label: "Limited Liability Company" },
  { value: 3, label: "Partnership" },
  { value: 4, label: "Association" },
  { value: 5, label: "Non-Profit Organization" },
  { value: 6, label: "Government Organization" },
  { value: 7, label: "C Corporation" },
  { value: 8, label: "S Corporation" },
];

export const ENTITY_CLASSIFICATION = [
  { value: 0, label: "Private Entity" },
  { value: 1, label: "Public Entity" },
];

export const US_STATES = [
  "AL",
  "AK",
  "AZ",
  "AR",
  "CA",
  "CO",
  "CT",
  "DE",
  "FL",
  "GA",
  "HI",
  "ID",
  "IL",
  "IN",
  "IA",
  "KS",
  "KY",
  "LA",
  "ME",
  "MD",
  "MA",
  "MI",
  "MN",
  "MS",
  "MO",
  "MT",
  "NE",
  "NV",
  "NH",
  "NJ",
  "NM",
  "NY",
  "NC",
  "ND",
  "OH",
  "OK",
  "OR",
  "PA",
  "RI",
  "SC",
  "SD",
  "TN",
  "TX",
  "UT",
  "VT",
  "VA",
  "WA",
  "WV",
  "WI",
  "WY",
];

export const BUSINESS_TYPE_CHECKS = {
  isSoleProprietor: (type: number) => type === 0,
  isCorporation: (type: number) => type === 1 || type === 7 || type === 8,
  isLLC: (type: number) => type === 2,
  isPartnership: (type: number) => type === 3,
  isAssociation: (type: number) => type === 4,
  isNonProfit: (type: number) => type === 5,
  isGovernment: (type: number) => type === 6,
  isCCorporation: (type: number) => type === 7,
  isSCorporation: (type: number) => type === 8,
  requiresCorporateStructure: (type: number) => type !== 0, // All except sole proprietorship
  isPublicEntity: (type: number) => type === 5 || type === 6, // Non-Profit and Government
  isPrivateEntity: (type: number) => type !== 5 && type !== 6, // All except Non-Profit and Government
};
