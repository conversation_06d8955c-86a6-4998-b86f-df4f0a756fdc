import { useDispatch } from "react-redux";
import { prevStep } from "../../redux/slices/onboardingSlice.ts";
import { BankVerificationSection, BankAccountTermsSection, BankAccountInfoSection } from "./sections";
import { useBankAccountForm } from "./hooks/useBankAccountForm";
import { handleBankAccountSubmit } from "./utils/bankAccountHelpers";

const BankAccountForm = () => {
  const dispatch = useDispatch();
  const {
    errors,
    termsAccepted,
    setTermsAccepted,
    verificationMethod,
    setVerificationMethod,
    uploadedFile,
    uploadPreview,
    plaidAccountData,
    isSoleProprietor,
    account,
    handlePlaidSuccess,
    validateForm,
    handleChange,
    handleFileChange,
    handlePreviewChange,
  } = useBankAccountForm();

  const handleSubmit = (e: React.FormEvent) => {
    handleBankAccountSubmit(e, validateForm, verificationMethod, uploadedFile, plaidAccountData, dispatch);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">Bank Account Information</h1>
            <p className="text-gray-600 mt-1">Enter your business bank account details for payment processing</p>
          </div>

          <form onSubmit={handleSubmit} className="px-8 py-8">
            <BankVerificationSection
              verificationMethod={verificationMethod}
              onVerificationMethodChange={setVerificationMethod}
              uploadedFile={uploadedFile}
              onFileChange={handleFileChange}
              uploadPreview={uploadPreview}
              onPreviewChange={handlePreviewChange}
              errors={errors}
              onPlaidSuccess={handlePlaidSuccess}
              account={account}
              onChange={handleChange}
              isSoleProprietor={isSoleProprietor}
            />

            <BankAccountInfoSection />

            <BankAccountTermsSection termsAccepted={termsAccepted} onTermsChange={setTermsAccepted} errors={errors} />

            <div className="border-t border-gray-200 pt-6 flex justify-between">
              <button
                type="button"
                onClick={() => dispatch(prevStep())}
                className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200"
              >
                Previous
              </button>
              <button
                type="submit"
                className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200"
              >
                Review & Submit
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default BankAccountForm;
