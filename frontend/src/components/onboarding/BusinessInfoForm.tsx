import { BusinessDetailsSection, ContactSection, AddressSection } from "./sections";
import { useBusinessInfoForm } from "./hooks/useBusinessInfoForm";
import { FormContainer, FormHeader, FormActions } from "./components";

const BusinessInfoForm = () => {
  const { formData, errors, handleSubmit, handleChange, handleFillDemoData } = useBusinessInfoForm();

  return (
    <FormContainer>
      <FormHeader title="Business Information" subtitle="Tell us about your business" onFillDemoData={handleFillDemoData} />

      <form onSubmit={handleSubmit} className="px-8 py-8">
        <BusinessDetailsSection formData={formData} errors={errors} onChange={handleChange} />
        <ContactSection formData={formData} errors={errors} onChange={handleChange} />
        <AddressSection formData={formData} errors={errors} onChange={handleChange} />

        <FormActions submitText="Continue to Owner Information" />
      </form>
    </FormContainer>
  );
};

export default BusinessInfoForm;
