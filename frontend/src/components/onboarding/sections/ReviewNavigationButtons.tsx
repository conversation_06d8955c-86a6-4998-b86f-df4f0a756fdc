interface IpDetectionResult {
  isLoading: boolean;
  isValid: boolean;
  source: string | null;
}

interface ReviewNavigationButtonsProps {
  isSubmitting: boolean;
  ipDetection: IpDetectionResult;
  onPrevious: () => void;
  onSubmit: () => void;
}

export const ReviewNavigationButtons = ({ isSubmitting, ipDetection, onPrevious, onSubmit }: ReviewNavigationButtonsProps) => {
  const isSubmitDisabled = isSubmitting || ipDetection.isLoading || (!ipDetection.isValid && ipDetection.source !== "fallback");

  const getSubmitTooltip = () => {
    if (ipDetection.isLoading) {
      return "Please wait while we detect your IP address...";
    }
    if (!ipDetection.isValid && ipDetection.source !== "fallback") {
      return "IP address detection failed. Please refresh the page and try again.";
    }
    return undefined;
  };

  return (
    <div className="border-t border-gray-200 pt-6 flex justify-between">
      <button
        type="button"
        onClick={onPrevious}
        disabled={isSubmitting}
        className="bg-gray-100 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-200 focus:ring-4 focus:ring-gray-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        Previous
      </button>
      <button
        onClick={onSubmit}
        disabled={isSubmitDisabled}
        className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 focus:ring-4 focus:ring-blue-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
        title={getSubmitTooltip()}
      >
        {isSubmitting && (
          <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        )}
        <span>{isSubmitting ? "Submitting..." : "Submit Application"}</span>
      </button>
    </div>
  );
};
