import { Member } from "../constants/ownerConstants";

interface PrimaryContactSectionProps {
  member: Member;
  index: number;
  onPrimaryChange: (index: number) => void;
  error?: string;
}

export const PrimaryContactSection = ({ member, index, onPrimaryChange, error }: PrimaryContactSectionProps) => {
  const isPrimary = member.primary === "1";

  return (
    <div className={`mb-6 rounded-lg p-4 border ${isPrimary ? "bg-green-50 border-green-200" : "bg-blue-50 border-blue-200"}`}>
      <h3 className={`text-sm font-semibold mb-2 ${isPrimary ? "text-green-900" : "text-blue-900"}`}>
        Primary Contact Designation
        {isPrimary && (
          <span className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            Primary Contact
          </span>
        )}
      </h3>
      <label className="flex items-center space-x-3 cursor-pointer">
        <input
          type="radio"
          name="primaryContact"
          checked={isPrimary}
          onChange={() => onPrimaryChange(index)}
          className={`w-4 h-4 border-gray-300 focus:ring-2 ${
            isPrimary ? "text-green-600 focus:ring-green-500" : "text-blue-600 focus:ring-blue-500"
          }`}
        />
        <span className="text-sm font-medium text-gray-700">
          {isPrimary ? "This principal is the primary contact" : "Select as primary contact for the business"}
        </span>
      </label>
      <p className="text-xs text-gray-600 mt-2">
        The primary contact will receive all business communications and account notifications.
        {!isPrimary && " Only one principal can be designated as primary."}
      </p>
      {error && <p className="text-red-600 text-sm mt-1">{error}</p>}
    </div>
  );
};
