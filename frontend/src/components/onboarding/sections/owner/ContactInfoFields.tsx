import { TextInput } from "../../form-fields";
import { Member } from "../../constants/ownerConstants";
import { formatPhone } from "../../utils/formatting";

interface ContactInfoFieldsProps {
  member: Member;
  index: number;
  onFieldChange: (index: number, field: string, value: string | number) => void;
  errors: Record<string, string>;
}

export const ContactInfoFields = ({ member, index, onFieldChange, errors }: ContactInfoFieldsProps) => {
  return (
    <div className="mb-8">
      <h3 className="text-md font-medium text-gray-900 mb-4">Contact Information</h3>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TextInput
          label="Personal Email"
          value={member.email || ""}
          onChange={(value) => onFieldChange(index, "email", value)}
          placeholder="<EMAIL>"
          type="email"
          error={errors[`member${index}.email`]}
          required
        />

        <TextInput
          label="Personal Phone"
          value={formatPhone(member.phone || "")}
          onChange={(value) => onFieldChange(index, "phone", value.replace(/\D/g, ""))}
          placeholder="(*************"
          type="tel"
          maxLength={14}
          error={errors[`member${index}.phone`]}
          required
        />
      </div>
    </div>
  );
};