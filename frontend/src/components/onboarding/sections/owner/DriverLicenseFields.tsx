import { TextInput, SelectInput } from "../../form-fields";
import { Member } from "../../constants/ownerConstants";
import { US_STATES } from "../../constants/businessConstants";

interface DriverLicenseFieldsProps {
  member: Member;
  index: number;
  onFieldChange: (index: number, field: string, value: string | number) => void;
  errors: Record<string, string>;
}

export const DriverLicenseFields = ({ member, index, onFieldChange, errors }: DriverLicenseFieldsProps) => {
  const stateOptions = US_STATES.map((state) => ({ value: state, label: state }));

  return (
    <div className="mb-8">
      <h3 className="text-md font-medium text-gray-900 mb-4">Driver&apos;s License Information</h3>
      <p className="text-gray-600 mb-4">Required for identity verification and KYC compliance</p>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TextInput
          label="Driver's License Number"
          value={member.dl || ""}
          onChange={(value) => onFieldChange(index, "dl", value)}
          placeholder="********"
          error={errors[`member${index}.dl`]}
          required
        />

        <SelectInput
          label="Issuing State"
          value={member.dlstate || ""}
          onChange={(value) => onFieldChange(index, "dlstate", value as string)}
          options={stateOptions}
          placeholder="Select state"
          error={errors[`member${index}.dlstate`]}
          required
        />
      </div>
    </div>
  );
};