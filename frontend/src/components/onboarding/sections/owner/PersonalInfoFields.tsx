import { TextInput } from "../../form-fields";
import { Member, OWNERSHIP_CONSTANTS } from "../../constants/ownerConstants";
import { formatSSN } from "../../utils/formatting";

interface PersonalInfoFieldsProps {
  member: Member;
  index: number;
  onFieldChange: (index: number, field: string, value: string | number) => void;
  errors: Record<string, string>;
}

export const PersonalInfoFields = ({ member, index, onFieldChange, errors }: PersonalInfoFieldsProps) => {
  return (
    <div className="mb-8">
      <h3 className="text-md font-medium text-gray-900 mb-4">Personal Information</h3>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TextInput
          label="First Name"
          value={member.first || ""}
          onChange={(value) => onFieldChange(index, "first", value)}
          placeholder="John"
          error={errors[`member${index}.first`]}
          required
        />

        <TextInput
          label="Middle Name"
          value={member.middle || ""}
          onChange={(value) => onFieldChange(index, "middle", value)}
          placeholder="Optional"
        />

        <TextInput
          label="Last Name"
          value={member.last || ""}
          onChange={(value) => onFieldChange(index, "last", value)}
          placeholder="Smith"
          error={errors[`member${index}.last`]}
          required
        />

        <TextInput
          label="Business Title"
          value={member.title || ""}
          onChange={(value) => onFieldChange(index, "title", value)}
          placeholder="CEO, Owner, President, etc."
          error={errors[`member${index}.title`]}
          required
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Date of Birth *</label>
          <input
            type="date"
            value={
              member.dob
                ? member.dob.length === 8
                  ? `${member.dob.slice(0, 4)}-${member.dob.slice(4, 6)}-${member.dob.slice(6, 8)}`
                  : member.dob
                : ""
            }
            onChange={(e) => onFieldChange(index, "dob", e.target.value.replace(/-/g, ""))}
            className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
              errors[`member${index}.dob`] ? "border-red-300 bg-red-50" : "border-gray-300"
            }`}
          />
          {errors[`member${index}.dob`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.dob`]}</p>}
          <p className="text-gray-500 text-sm mt-1">Must be at least 18 years old</p>
        </div>

        <TextInput
          label="Social Security Number"
          value={formatSSN(member.ssn || "")}
          onChange={(value) => onFieldChange(index, "ssn", value.replace(/\D/g, ""))}
          placeholder="***********"
          maxLength={11}
          error={errors[`member${index}.ssn`]}
          required
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Ownership Percentage *</label>
          <div className="relative">
            <input
              type="number"
              min="0"
              max="100"
              step="0.01"
              value={
                member.ownership === 0 || member.ownership === null || member.ownership === undefined
                  ? ""
                  : (member.ownership / OWNERSHIP_CONSTANTS.PERCENTAGE_DIVISOR).toString()
              }
              onChange={(e) => {
                const inputValue = e.target.value.trim();

                if (inputValue === "") {
                  onFieldChange(index, "ownership", 0);
                  return;
                }

                const percentage = parseFloat(inputValue);

                if (!isNaN(percentage)) {
                  const basisPoints = Math.round(percentage * OWNERSHIP_CONSTANTS.PERCENTAGE_DIVISOR);
                  onFieldChange(index, "ownership", basisPoints);
                }
              }}
              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-8 ${
                errors[`member${index}.ownership`] ? "border-red-300 bg-red-50" : "border-gray-300"
              }`}
              placeholder="25.00"
              data-error-key={`member${index}.ownership`}
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              <span className="text-gray-500">%</span>
            </div>
          </div>
          <p className="text-xs text-gray-600 mt-1">
            Enter the ownership percentage for this principal. Total ownership across all principals must equal 100%.
          </p>
          {errors[`member${index}.ownership`] && <p className="text-red-600 text-sm mt-1">{errors[`member${index}.ownership`]}</p>}
          {member.ownership < OWNERSHIP_CONSTANTS.MINIMUM_REQUIRED_OWNERSHIP && member.ownership > 0 && (
            <p className="text-amber-600 text-sm mt-1">Only principals with 25% or more ownership are required</p>
          )}
        </div>
      </div>
    </div>
  );
};