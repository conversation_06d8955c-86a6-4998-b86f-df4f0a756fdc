import { Member } from "../../constants/ownerConstants";

interface ResponsibilityStatusProps {
  member: Member;
  index: number;
  onFieldChange: (index: number, field: string, value: string | number) => void;
}

export const ResponsibilityStatus = ({ member, index, onFieldChange }: ResponsibilityStatusProps) => {
  return (
    <div>
      <h3 className="text-md font-medium text-gray-900 mb-4">Responsibilities & Status</h3>
      <div className="space-y-4">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={member.significantResponsibility === 1}
              onChange={(e) => onFieldChange(index, "significantResponsibility", e.target.checked ? 1 : 0)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label className="text-sm font-medium text-gray-700">This person has significant responsibility for the business</label>
          </div>
          <p className="text-gray-600 text-sm mt-2 ml-7">
            Check this if the person is a CEO, CFO, Owner, VP, managing member, or similar controlling authority.
          </p>
        </div>

        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              checked={member.politicallyExposed === 1}
              onChange={(e) => onFieldChange(index, "politicallyExposed", e.target.checked ? 1 : 0)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            <label className="text-sm font-medium text-gray-700">This person is politically exposed</label>
          </div>
          <p className="text-gray-600 text-sm mt-2 ml-7">
            A politically exposed person is someone who, through their prominent position or influence, is more susceptible to being involved in
            bribery or corruption.
          </p>
        </div>
      </div>
    </div>
  );
};