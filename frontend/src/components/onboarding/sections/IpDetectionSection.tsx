interface IpDetectionResult {
  ip: string;
  isLoading: boolean;
  isValid: boolean;
  source: string | null;
  error: string | null;
}

interface IpDetectionSectionProps {
  ipDetection: IpDetectionResult;
}

export const IpDetectionSection = ({ ipDetection }: IpDetectionSectionProps) => {
  return (
    <div className="border border-gray-200 rounded-lg p-6 mb-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Security Information</h3>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-700">IP Address:</span>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-900 font-mono">{ipDetection.ip}</span>
            {ipDetection.isLoading && (
              <div className="flex items-center space-x-1">
                <div className="w-3 h-3 border border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-xs text-blue-600">Detecting...</span>
              </div>
            )}
            {!ipDetection.isLoading && ipDetection.isValid && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">✓ Valid</span>
            )}
            {!ipDetection.isLoading && !ipDetection.isValid && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">⚠ Fallback</span>
            )}
          </div>
        </div>
        {ipDetection.source && (
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Detection Source:</span>
            <span className="text-sm text-gray-600 capitalize">{ipDetection.source}</span>
          </div>
        )}
        {ipDetection.error && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">{ipDetection.error}</p>
              </div>
            </div>
          </div>
        )}
        <p className="text-xs text-gray-500">
          Your IP address is captured for security and compliance purposes as required by payment processing regulations.
        </p>
      </div>
    </div>
  );
};
