import { ReactNode } from "react";

interface ReviewPageLayoutProps {
  title: string;
  subtitle: string;
  children: ReactNode;
}

export const ReviewPageLayout = ({ title, subtitle, children }: ReviewPageLayoutProps) => {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Header */}
          <div className="border-b border-gray-200 px-8 py-6">
            <h1 className="text-2xl font-semibold text-gray-900">{title}</h1>
            <p className="text-gray-600 mt-1">{subtitle}</p>
          </div>

          <div className="px-8 py-8">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};