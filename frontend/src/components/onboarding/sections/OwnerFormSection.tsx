import { Member, OWNERSHIP_CONSTANTS } from "../constants/ownerConstants";
import { PrimaryContactSection } from "./PrimaryContactSection";
import { 
  PersonalInfoFields, 
  ContactInfoFields, 
  OwnerAddressFields, 
  DriverLicenseFields, 
  ResponsibilityStatus 
} from "./owner";

interface OwnerFormSectionProps {
  member: Member;
  index: number;
  canRemove: boolean;
  onFieldChange: (index: number, field: string, value: string | number) => void;
  onRemove: (index: number) => void;
  onPrimaryChange: (index: number) => void;
  errors: Record<string, string>;
}

export const OwnerFormSection = ({ 
  member, 
  index, 
  canRemove, 
  onFieldChange, 
  onRemove, 
  onPrimaryChange, 
  errors 
}: OwnerFormSectionProps) => {
  return (
    <div className="mb-10 border border-gray-200 rounded-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-medium text-gray-900">
          Principal {index + 1} Information
          {member.ownership > 0 && (
            <span className="text-sm text-gray-600 ml-2">
              ({(member.ownership / OWNERSHIP_CONSTANTS.PERCENTAGE_DIVISOR).toFixed(2)}% ownership)
            </span>
          )}
        </h2>
        {canRemove && (
          <button 
            type="button" 
            onClick={() => onRemove(index)} 
            className="text-red-600 hover:text-red-800 text-sm font-medium"
          >
            Remove Principal
          </button>
        )}
      </div>

      <PrimaryContactSection 
        member={member} 
        index={index} 
        onPrimaryChange={onPrimaryChange} 
        error={errors.primaryContact} 
      />

      <PersonalInfoFields 
        member={member} 
        index={index} 
        onFieldChange={onFieldChange} 
        errors={errors} 
      />

      <ContactInfoFields 
        member={member} 
        index={index} 
        onFieldChange={onFieldChange} 
        errors={errors} 
      />

      <OwnerAddressFields 
        member={member} 
        index={index} 
        onFieldChange={onFieldChange} 
        errors={errors} 
      />

      <DriverLicenseFields 
        member={member} 
        index={index} 
        onFieldChange={onFieldChange} 
        errors={errors} 
      />

      <ResponsibilityStatus 
        member={member} 
        index={index} 
        onFieldChange={onFieldChange} 
      />
    </div>
  );
};