import { Member, OWNERSHIP_CONSTANTS } from "../constants/ownerConstants";

interface OwnershipSummaryProps {
  members: Member[];
}

export const OwnershipSummary = ({ members }: OwnershipSummaryProps) => {
  const totalOwnership = members.reduce((sum, m) => sum + (m.ownership || 0), 0);
  const totalPercentage = totalOwnership / OWNERSHIP_CONSTANTS.PERCENTAGE_DIVISOR;
  const isValid = totalOwnership === OWNERSHIP_CONSTANTS.FULL_OWNERSHIP;
  const isOver = totalOwnership > OWNERSHIP_CONSTANTS.FULL_OWNERSHIP;
  const difference = Math.abs(totalPercentage - 100);

  const getStatusIcon = () => {
    if (isValid) {
      return (
        <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      );
    } else {
      return (
        <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
      );
    }
  };

  const getStatusMessage = () => {
    if (isValid) {
      return "Perfect! All ownership percentages are properly allocated.";
    } else if (isOver) {
      return `Total ownership exceeds 100% by ${difference.toFixed(2)}%. Please reduce ownership percentages.`;
    } else {
      return `Total ownership is ${difference.toFixed(2)}% short of 100%. Please increase ownership percentages.`;
    }
  };

  return (
    <div className={`mb-6 p-4 rounded-lg border ${isValid ? "bg-green-50 border-green-200" : "bg-red-50 border-red-200"}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-0.5">{getStatusIcon()}</div>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <h3 className={`text-sm font-semibold ${isValid ? "text-green-800" : "text-red-800"}`}>Ownership Distribution Summary</h3>
            <span className={`text-lg font-bold ${isValid ? "text-green-700" : "text-red-700"}`}>{totalPercentage.toFixed(2)}%</span>
          </div>
          <p className={`text-sm ${isValid ? "text-green-700" : "text-red-700"}`}>{getStatusMessage()}</p>
          {members.length > 1 && (
            <div className="mt-3 space-y-1">
              {members.map((member, index) => (
                <div key={index} className="flex justify-between text-xs text-gray-600">
                  <span>
                    Principal {index + 1}: {member.first || "Unnamed"} {member.last || "Principal"}
                  </span>
                  <span className="font-medium">{((member.ownership || 0) / OWNERSHIP_CONSTANTS.PERCENTAGE_DIVISOR).toFixed(2)}%</span>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
