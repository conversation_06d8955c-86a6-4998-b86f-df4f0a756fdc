import { useState } from "react";
import { toast } from "sonner";

interface CodeBlockProps {
  code: string;
  label: string;
  showCopyButton?: boolean;
  maxHeight?: string;
}

export const CodeBlock = ({ 
  code, 
  label, 
  showCopyButton = true, 
  maxHeight = "max-h-96"
}: CodeBlockProps) => {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    toast.success(`${label} copied to clipboard!`);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="relative">
      {showCopyButton && (
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
          <p className="text-slate-600 leading-relaxed">{label}:</p>
          <button
            onClick={copyToClipboard}
            className="px-4 py-2 bg-slate-800 text-white text-sm rounded-lg hover:bg-slate-900 transition-colors flex-shrink-0 self-start sm:self-auto"
          >
            {copied ? "Copied!" : "Copy Code"}
          </button>
        </div>
      )}
      <div className={`bg-slate-900 text-slate-100 p-4 sm:p-6 rounded-lg text-sm overflow-x-auto ${maxHeight} overflow-y-auto border`}>
        <pre className="whitespace-pre-wrap sm:whitespace-pre">{code}</pre>
      </div>
    </div>
  );
};