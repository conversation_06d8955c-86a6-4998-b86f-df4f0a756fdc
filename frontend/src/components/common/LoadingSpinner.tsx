interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: 'blue' | 'white' | 'gray' | 'custom';
  customColor?: string;
  text?: string;
  textColor?: string;
  className?: string;
}

const sizeClasses = {
  small: 'h-5 w-5 border-2',
  medium: 'h-8 w-8 border-4',
  large: 'h-10 w-10 border-4'
} as const;

const colorClasses = {
  blue: 'border-blue-600',
  white: 'border-white',
  gray: 'border-gray-600'
} as const;

export const LoadingSpinner = ({ 
  size = 'medium', 
  color = 'blue', 
  customColor,
  text,
  textColor,
  className = ''
}: LoadingSpinnerProps) => {
  const spinnerStyle = color === 'custom' && customColor 
    ? { borderColor: `${customColor} transparent ${customColor} ${customColor}` }
    : undefined;
  
  const textStyle = textColor ? { color: textColor } : undefined;
  
  return (
    <div className={`flex flex-col items-center justify-center ${className}`} role="status" aria-live="polite">
      <div 
        className={`animate-spin ${sizeClasses[size]} ${color !== 'custom' ? colorClasses[color] : ''} border-t-transparent rounded-full`}
        style={spinnerStyle}
        aria-hidden="true"
      />
      {text && (
        <p className="mt-4 text-gray-600" style={textStyle}>{text}</p>
      )}
      <span className="sr-only">{text || 'Loading...'}</span>
    </div>
  );
};