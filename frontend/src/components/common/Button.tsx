import { ButtonHTMLAttributes, ReactNode } from 'react';

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost' | 'blue';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  icon?: ReactNode;
  children: ReactNode;
  className?: string;
}

const variantClasses = {
  primary: 'bg-[#364F6B] hover:bg-[#2A3F59] text-white shadow-md hover:shadow-lg',
  blue: 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg',
  secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800 shadow-sm hover:shadow',
  danger: 'bg-red-600 hover:bg-red-700 text-white shadow-md hover:shadow-lg',
  ghost: 'bg-transparent hover:bg-gray-100 text-gray-700'
} as const;

const sizeClasses = {
  small: 'px-3 py-1.5 text-sm',
  medium: 'px-4 py-2 text-base',
  large: 'px-6 py-3 text-lg'
} as const;

export const Button = ({
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  icon,
  children,
  className = '',
  ...props
}: ButtonProps) => {
  const isDisabled = disabled || loading;
  
  return (
    <button
      disabled={isDisabled}
      className={`font-medium rounded-md transition-all ${variantClasses[variant]} ${sizeClasses[size]} ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`.trim()}
      {...props}
    >
      {loading ? (
        <span className="flex items-center justify-center">
          <div className="animate-spin -ml-1 mr-3 h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
          Loading...
        </span>
      ) : (
        <span className={icon ? 'flex items-center gap-2' : ''}>
          {icon}
          {children}
        </span>
      )}
    </button>
  );
};