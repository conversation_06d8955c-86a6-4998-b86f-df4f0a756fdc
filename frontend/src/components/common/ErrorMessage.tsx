interface ErrorMessageProps {
  message: string;
  variant?: 'inline' | 'banner';
  className?: string;
}

export const ErrorMessage = ({ 
  message, 
  variant = 'inline',
  className = '' 
}: ErrorMessageProps) => {
  if (variant === 'banner') {
    return (
      <div className={`mb-6 p-4 bg-red-50 text-red-700 rounded-lg border-l-4 border-red-500 animate-fadeIn ${className}`}>
        <div className="flex items-start">
          <svg className="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <p className="font-medium">Error</p>
            <p className="text-sm mt-1">{message}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-4 bg-red-50 text-red-800 rounded-md ${className}`}>
      <p>{message}</p>
    </div>
  );
};