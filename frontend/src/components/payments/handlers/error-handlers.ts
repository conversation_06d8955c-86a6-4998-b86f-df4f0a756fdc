import { toast } from "sonner";
import { PaymentError } from "../types/payfields.types";
import { postMessageToParent } from "../utils/iframe-communication";
import { MESSAGES, PAYMENT_EVENTS } from "./constants";
import { isPaymentCancelled } from "./helpers";

export const createPaymentFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: PaymentError) => {
    if (isPaymentCancelled(err)) {
      postMessageToParent(PAYMENT_EVENTS.CANCELLED, {
        message: MESSAGES.USER_CANCELLED,
        details: err,
      });
      return;
    }

    let errorMessage: string = MESSAGES.PAYMENT_PROCESSING_FAILED;
    if (err && Array.isArray(err.errors)) {
      const fieldErrors = err.errors.map((e) => `${e.field}: ${e.msg}`).join(", ");
      errorMessage = `Payment failed: ${fieldErrors}`;
    } else if (err && err.message) {
      errorMessage = err.message;
    }

    toast.error(errorMessage);
    postMessageToParent(PAYMENT_EVENTS.FAILURE, {
      error: errorMessage,
      details: err,
    });

    if (onFailure) onFailure(err);
  };
};

export const createValidationFailureHandler = (onFailure?: (error: unknown) => void) => {
  return (err: unknown) => {
    const validationMessage = MESSAGES.PAYMENT_VALIDATION_FAILED;
    toast.error(MESSAGES.VALIDATION_CHECK_DETAILS);

    postMessageToParent(PAYMENT_EVENTS.VALIDATION_FAILURE, {
      error: validationMessage,
      details: err,
    });

    if (onFailure) onFailure({ message: validationMessage, details: err });
  };
};

export const createPaymentFinishHandler = () => {
  return (response: unknown) => {
    postMessageToParent(PAYMENT_EVENTS.FINISHED, { data: response });
  };
};