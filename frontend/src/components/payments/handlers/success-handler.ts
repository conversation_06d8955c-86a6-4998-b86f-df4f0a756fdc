import { toast } from "sonner";
import { processTokenPayment, cleanupIntegrationToken } from "../../../services/api";
import { PayFieldsConfig, BillingAddress, PaymentResponse } from "../types/payfields.types";
import { PaymentInfo } from "../../../types/payment";
import { postMessageToParent } from "../utils/iframe-communication";
import { MESSAGES, PAYMENT_EVENTS, PAYMENT_MODES } from "./constants";
import { extractPaymentToken, createCustomerPayload } from "./helpers";

const getIntegrationTokenFromUrl = (): string | null => {
  try {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get("token");
  } catch {
    return null;
  }
};

const performTokenCleanup = async (integrationToken: string): Promise<void> => {
  try {
    await cleanupIntegrationToken(integrationToken);
  } catch (error) {
    console.warn("Token cleanup failed:", error);
  }
};

export const createPaymentSuccessHandler = (
  config: PayFieldsConfig,
  paymentInfo: PaymentInfo | null,
  billingAddress?: BillingAddress,
  onSuccess?: (response: unknown) => void,
  setIsProcessingPayment?: (value: boolean) => void
) => {
  return async (response: PaymentResponse) => {
    if (!response) {
      throw new Error(MESSAGES.RESPONSE_NULL);
    }

    const isTokenMode = config.mode === PAYMENT_MODES.TOKEN;

    if (!isTokenMode) {
      if (setIsProcessingPayment) setIsProcessingPayment(false);
      toast.success(MESSAGES.SUCCESS);
      postMessageToParent(PAYMENT_EVENTS.SUCCESS, { data: response });
      if (onSuccess) onSuccess(response);
      return;
    }

    const { token: extractedToken, tokenId: extractedTokenId } = extractPaymentToken(response);

    if (!extractedToken) {
      const isGooglePayEnabled = config.googlePayConfig?.enabled;

      if (isGooglePayEnabled) {
        // Cleanup integration token after successful Google Pay
        const integrationToken = getIntegrationTokenFromUrl();
        if (integrationToken) {
          void performTokenCleanup(integrationToken);
        }

        if (setIsProcessingPayment) setIsProcessingPayment(false);
        toast.success(MESSAGES.GOOGLE_PAY_SUCCESS);
        postMessageToParent(PAYMENT_EVENTS.SUCCESS, {
          data: response,
          paymentMethod: "google_pay",
          googlePayProcessed: true,
        });
        if (onSuccess) onSuccess(response);
        return;
      }

      const errorMessage = MESSAGES.TOKEN_GENERATION_FAILED;
      if (setIsProcessingPayment) setIsProcessingPayment(false);
      toast.error(errorMessage);
      postMessageToParent(PAYMENT_EVENTS.ERROR, {
        error: errorMessage,
        tokenGenerationFailed: true,
      });

      if (onSuccess) {
        onSuccess({
          error: errorMessage,
          tokenGenerationFailed: true,
        });
      }
      throw new Error(errorMessage);
    }

    const actualAmount = paymentInfo?.amount || config.amount;
    const paymentDescription = paymentInfo?.description || config.description;

    try {
      const paymentResult = await processTokenPayment({
        merchantId: config.merchantId,
        token: extractedToken,
        tokenId: extractedTokenId || extractedToken,
        amount: actualAmount,
        description: paymentDescription,
        customerInfo: createCustomerPayload(billingAddress),
      });

      if (!paymentResult.success) {
        throw new Error(paymentResult.message || MESSAGES.TOKEN_PROCESSING_FAILED);
      }

      // Cleanup integration token after successful card payment
      const integrationToken = getIntegrationTokenFromUrl();
      if (integrationToken) {
        void performTokenCleanup(integrationToken);
      }

      if (setIsProcessingPayment) setIsProcessingPayment(false);
      toast.success(MESSAGES.SUCCESS);

      const successResponse = {
        ...response,
        transaction: paymentResult.transaction,
        merchantInfo: paymentResult.merchantInfo,
        tokenProcessed: true,
      };

      postMessageToParent(PAYMENT_EVENTS.SUCCESS, { data: successResponse });
      if (onSuccess) onSuccess(successResponse);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : MESSAGES.TOKEN_PROCESSING_FAILED;
      if (setIsProcessingPayment) setIsProcessingPayment(false);
      toast.error(`${MESSAGES.PAYMENT_FAILED_PREFIX}${errorMessage}`);

      const errorResponse = {
        error: errorMessage,
        tokenGenerated: true,
        tokenProcessingFailed: true,
      };

      postMessageToParent(PAYMENT_EVENTS.ERROR, errorResponse);
      if (onSuccess) onSuccess(errorResponse);
      throw error;
    }
  };
};
