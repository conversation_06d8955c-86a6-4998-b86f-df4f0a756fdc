export const MESSAGES = {
  SUCCESS: "Payment processed successfully!",
  GOOGLE_PAY_SUCCESS: "Payment processed with Google Pay!",
  RESPONSE_NULL: "Payment response is null or undefined",
  TOKEN_GENERATION_FAILED: "Token generation failed: No token received from PayFields",
  TOKEN_PROCESSING_FAILED: "Token payment processing failed",
  PAYMENT_FAILED_PREFIX: "Payment failed: ",
  PAYMENT_PROCESSING_FAILED: "Payment processing failed. Please try again.",
  PAYMENT_VALIDATION_FAILED: "Payment validation failed. Please check your card details.",
  VALIDATION_CHECK_DETAILS: "Please check your card details",
  USER_CANCELLED: "Payment cancelled by user",
} as const;

export const PAYMENT_EVENTS = {
  SUCCESS: "PAYMENT_SUCCESS",
  ERROR: "PAYMENT_ERROR",
  CANCELLED: "PAYMENT_CANCELLED",
  FAILURE: "PAYMENT_FAILURE",
  VALIDATION_FAILURE: "PAYMENT_VALIDATION_FAILURE",
  FINISHED: "PAYMENT_FINISHED",
} as const;

export const PAYMENT_MODES = {
  TOKEN: "token",
} as const;

export const CANCELLATION_CODES = {
  CANCELED: "CANCELED",
  CANCELLED: "CANCELLED",
} as const;

export const CANCELLATION_KEYWORDS = [
  "canceled",
  "cancelled",
  "user canceled",
  "user cancelled",
  "user_canceled",
  "user_cancelled",
  "aborted",
  "dismissed",
  "closed",
  "user closed",
  "user dismissed",
  "payment_canceled",
  "payment_cancelled",
  "google pay canceled",
  "google pay cancelled",
] as const;