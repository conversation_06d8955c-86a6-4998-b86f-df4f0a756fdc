import { PaymentResponse, BillingAddress, PaymentError } from "../types/payfields.types";
import { CANCELLATION_CODES, CANCELLATION_KEYWORDS } from "./constants";

export const extractPaymentToken = (response: PaymentResponse): { token?: string; tokenId?: string } => {
  if (response.data && Array.isArray(response.data) && response.data.length > 0) {
    const firstDataItem = response.data[0];
    return {
      token: firstDataItem?.token,
      tokenId: firstDataItem?.id,
    };
  }

  if (response.token) {
    return {
      token: response.token,
      tokenId: response.id || response.token,
    };
  }

  if (response.details) {
    return {
      token: response.details.token,
      tokenId: response.details.id || response.details.token,
    };
  }

  return {};
};

export const createCustomerPayload = (billingAddress?: BillingAddress) => {
  if (!billingAddress) return undefined;

  return {
    name: `${billingAddress.firstName} ${billingAddress.lastName}`,
    email: billingAddress.email,
    address: {
      line1: billingAddress.line1,
      line2: billingAddress.line2,
      city: billingAddress.city,
      state: billingAddress.state,
      zip: billingAddress.zip,
      country: billingAddress.country,
    },
  };
};

export const isPaymentCancelled = (err: PaymentError): boolean => {
  if (!err) return false;

  if (err.statusCode === CANCELLATION_CODES.CANCELED || err.statusCode === CANCELLATION_CODES.CANCELLED) {
    return true;
  }

  if (!err.message) return false;

  const message = err.message.toLowerCase();
  return CANCELLATION_KEYWORDS.some((keyword) => message.includes(keyword));
};