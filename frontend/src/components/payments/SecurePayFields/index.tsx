import { useRef, useState, useMemo } from "react";
import { CreditCard, Wallet } from "lucide-react";
import { usePayFields } from "../hooks/usePayFields";
import { SecurePayFieldsProps } from "../types/payfields.types";
import { LoadingSpinner } from "../../common/LoadingSpinner";
import { ErrorMessage } from "../../common/ErrorMessage";

const SecurePayFields = ({ config, paymentInfo, onSuccess, onFailure, className = "", billingAddress }: SecurePayFieldsProps) => {
  const cardNumberRef = useRef<HTMLDivElement>(null);
  const cardNameRef = useRef<HTMLDivElement>(null);
  const cardCvvRef = useRef<HTMLDivElement>(null);
  const cardExpirationRef = useRef<HTMLDivElement>(null);

  const googlePayAvailable = !!config?.googlePayConfig?.enabled;
  const [selectedMethod, setSelectedMethod] = useState<"card" | "googlePay">("card");

  const effectiveConfig = useMemo(() => {
    if (!config) return config;
    if (selectedMethod === "card") {
      return {
        ...config,
        googlePayConfig: config.googlePayConfig ? { ...config.googlePayConfig, enabled: false } : undefined,
      };
    }
    if (selectedMethod === "googlePay") {
      return {
        ...config,
        googlePayConfig: config.googlePayConfig ? { ...config.googlePayConfig, enabled: true } : undefined,
      };
    }
    return config;
  }, [config, selectedMethod]);

  const { scriptError, isSubmitting, isProcessingPayment, validationError, isLoading, handleSubmit } = usePayFields({
    config: effectiveConfig,
    paymentInfo,
    billingAddress,
    onSuccess,
    onFailure,
  });

  if (scriptError) {
    return <ErrorMessage message={scriptError} variant="inline" className={className} />;
  }

  const LoadingOverlayComponent = () => (
    <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10 rounded-lg">
      <LoadingSpinner size="medium" color="blue" text="Loading payment methods..." />
    </div>
  );

  const PaymentMethodSelector = () => {
    if (!googlePayAvailable) return null;

    return (
      <div className="space-y-3 mb-6">
        <p className="text-sm text-gray-600">Choose a payment method</p>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          <button
            type="button"
            onClick={() => setSelectedMethod("card")}
            className={`p-3 sm:p-4 border rounded-lg transition-colors text-left ${
              selectedMethod === "card"
                ? "border-blue-500 bg-blue-50 ring-2 ring-blue-200"
                : "border-gray-200 bg-white hover:bg-gray-50 hover:border-gray-300"
            }`}
          >
            <div className="flex items-center gap-3">
              <span
                className={`inline-flex items-center justify-center h-9 w-9 rounded-md ${
                  selectedMethod === "card" ? "bg-blue-100 text-blue-700" : "bg-gray-100 text-gray-700"
                }`}
              >
                <CreditCard className="h-5 w-5" />
              </span>
              <div>
                <p className={`text-sm font-medium ${selectedMethod === "card" ? "text-blue-900" : "text-gray-900"}`}>Card</p>
                <p className="text-xs text-gray-500">Enter card details securely</p>
              </div>
            </div>
          </button>

          <button
            type="button"
            onClick={() => setSelectedMethod("googlePay")}
            className={`p-3 sm:p-4 border rounded-lg transition-colors text-left ${
              selectedMethod === "googlePay"
                ? "border-blue-500 bg-blue-50 ring-2 ring-blue-200"
                : "border-gray-200 bg-white hover:bg-gray-50 hover:border-gray-300"
            }`}
          >
            <div className="flex items-center gap-3">
              <span
                className={`inline-flex items-center justify-center h-9 w-9 rounded-md ${
                  selectedMethod === "googlePay" ? "bg-blue-100 text-blue-700" : "bg-gray-100 text-gray-700"
                }`}
              >
                <Wallet className="h-5 w-5" />
              </span>
              <div>
                <p className={`text-sm font-medium ${selectedMethod === "googlePay" ? "text-blue-900" : "text-gray-900"}`}>Google Pay</p>
                <p className="text-xs text-gray-500">Fast checkout with Google</p>
              </div>
            </div>
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className={`secure-payfields relative ${className}`}>
      {validationError && <ErrorMessage message={validationError} variant="inline" className="mb-4" />}

      {isLoading && <LoadingOverlayComponent />}

      <PaymentMethodSelector />

      {/* Google Pay Section */}
      {selectedMethod === "googlePay" && config.googlePayConfig?.enabled && (
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-3">Pay with Google Pay</h3>
          <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
            {/* Google Pay button will be rendered here by PayFields */}
            <div id="googlePayButton" className="flex items-center justify-center min-h-[48px]"></div>
          </div>
        </div>
      )}

      {/* Card Payment Section - Always render DOM elements for PayFields */}
      <div className={`space-y-4 ${selectedMethod === "googlePay" ? "hidden" : ""}`}>
        <h3 className="text-lg font-medium text-gray-900 mb-3">Card Information</h3>

        <div>
          <label htmlFor="card-number" className="block mb-2 text-sm font-medium text-gray-700">
            Card Number
          </label>
          <div id="card-number" ref={cardNumberRef} className="h-12 border rounded-md"></div>
        </div>

        <div>
          <label htmlFor="card-name" className="block mb-2 text-sm font-medium text-gray-700">
            Cardholder Name
          </label>
          <div id="card-name" ref={cardNameRef} className="h-12 border rounded-md"></div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="card-expiration" className="block mb-2 text-sm font-medium text-gray-700">
              Expiration Date
            </label>
            <div id="card-expiration" ref={cardExpirationRef} className="h-12 border rounded-md"></div>
          </div>
          <div>
            <label htmlFor="card-cvv" className="block mb-2 text-sm font-medium text-gray-700">
              CVV
            </label>
            <div id="card-cvv" ref={cardCvvRef} className="h-12 border rounded-md"></div>
          </div>
        </div>

        <button
          onClick={handleSubmit}
          disabled={isSubmitting || isProcessingPayment}
          className={`w-full py-3 px-4 font-medium rounded-md transition-all ${
            isSubmitting || isProcessingPayment
              ? "bg-gray-400 cursor-not-allowed text-white"
              : "bg-[#364F6B] hover:bg-[#2A3F59] text-white shadow-md hover:shadow-lg"
          }`}
        >
          {isSubmitting || isProcessingPayment ? (
            <span className="flex items-center justify-center">
              <div className="animate-spin -ml-1 mr-3 h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
              Processing...
            </span>
          ) : (
            `Pay $${((paymentInfo?.amount || config.amount) / 100).toFixed(2)} ${paymentInfo?.currency || "USD"}`
          )}
        </button>
      </div>
    </div>
  );
};

export default SecurePayFields;
