import { PrimaryColor } from "../../../constants/colors";

export const PaymentFooter = () => {
  return (
    <div className="text-center mt-4 mb-8">
      <div className="flex flex-col md:flex-row items-center justify-center space-y-2 md:space-y-0 md:space-x-6 text-xs text-gray-500">
        <div className="flex items-center">
          <svg
            className="w-4 h-4 mr-2"
            style={{ color: PrimaryColor.hex }}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            ></path>
          </svg>
          Secured by Auth Clear Payment Processing
        </div>
        {/* <div className="flex items-center">
          <svg
            className="w-4 h-4 mr-2"
            style={{ color: SecondaryColor.hex }}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
            ></path>
          </svg>
          SSL Encrypted Connection
        </div> */}
        {/* <div className="flex items-center">
          <svg
            className="w-4 h-4 mr-2"
            style={{ color: PrimaryColor.hex }}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            ></path>
          </svg>
          24/7 Customer Support
        </div> */}
      </div>
    </div>
  );
};
