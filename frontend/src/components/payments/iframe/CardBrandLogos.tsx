interface CardBrandLogosProps {
  className?: string;
  showGooglePay?: boolean;
}

export const CardBrandLogos = ({ className = "", showGooglePay = false }: CardBrandLogosProps) => {
  return (
    <div className={`${className}`}>
      <p className="text-sm text-gray-600 mb-3">Accepted Payment Methods</p>
      <div className="flex items-center gap-2 flex-wrap">
        {/* Visa */}
        <div className="bg-white rounded-lg border border-gray-200 px-3 py-2 shadow-sm hover:shadow-md transition-shadow">
          <img 
            src="/paymentLogos/visa.svg" 
            alt="Visa" 
            className="h-8 w-auto object-contain"
          />
        </div>

        {/* Mastercard */}
        <div className="bg-white rounded-lg border border-gray-200 px-3 py-2 shadow-sm hover:shadow-md transition-shadow">
          <img 
            src="/paymentLogos/mastercard.svg" 
            alt="Mastercard" 
            className="h-8 w-auto object-contain"
          />
        </div>

        {/* American Express */}
        <div className="bg-white rounded-lg border border-gray-200 px-3 py-2 shadow-sm hover:shadow-md transition-shadow">
          <img 
            src="/paymentLogos/amex.svg" 
            alt="American Express" 
            className="h-8 w-auto object-contain"
          />
        </div>

        {/* Discover */}
        <div className="bg-white rounded-lg border border-gray-200 px-3 py-2 shadow-sm hover:shadow-md transition-shadow">
          <img 
            src="/paymentLogos/discover.svg" 
            alt="Discover" 
            className="h-8 w-auto object-contain"
          />
        </div>

        {/* Google Pay - Conditionally shown */}
        {showGooglePay && (
          <div className="bg-white rounded-lg border border-gray-200 px-3 py-2 shadow-sm hover:shadow-md transition-shadow">
            <img 
              src="/paymentLogos/googlepay.svg" 
              alt="Google Pay" 
              className="h-8 w-auto object-contain"
            />
          </div>
        )}
      </div>
      
      {/* Security note */}
      <div className="mt-3 flex items-center gap-2 text-xs text-gray-500">
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
          />
        </svg>
        <span>Your payment information is encrypted and secure</span>
      </div>
    </div>
  );
};