import type { PaymentInfo } from "../../../types/payment";

interface SuccessStateProps {
  paymentInfo: PaymentInfo | null;
}

export const SuccessState = ({ paymentInfo }: SuccessStateProps) => {
  return (
    <div className="min-h-screen bg-slate-50 flex items-center justify-center px-4">
      <div className="max-w-2xl w-full">
        <div className="mb-6 p-4 bg-green-50 text-green-700 rounded-lg border-l-4 border-green-500 animate-fadeIn">
          <p className="flex items-center">
            <svg
              className="w-5 h-5 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              ></path>
            </svg>
            Payment processed successfully!
          </p>
          {paymentInfo?.returnUrl && (
            <div className="flex items-center mt-3 text-sm">
              <svg
                className="animate-spin -ml-1 mr-2 h-4 w-4 text-green-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Redirecting you back to the merchant...
            </div>
          )}
        </div>
      </div>
    </div>
  );
};