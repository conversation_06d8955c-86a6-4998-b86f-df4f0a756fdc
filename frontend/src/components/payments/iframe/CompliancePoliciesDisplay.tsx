import { useState } from "react";
import { CompliancePolicies, URLOnlyPolicy } from "../../../types/payment";

interface CompliancePoliciesDisplayProps {
  policies: CompliancePolicies;
  termsAccepted: boolean;
  onTermsChange: (accepted: boolean) => void;
}

interface PolicyModalProps {
  policy: URLOnlyPolicy;
  isOpen: boolean;
  onClose: () => void;
}

const PolicyModal = ({ policy, isOpen, onClose }: PolicyModalProps) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">{policy.title}</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-4 overflow-y-auto max-h-[60vh]">
          <div className="prose prose-sm max-w-none">
            <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{policy.content || "No content available."}</p>

            {policy.url && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <p className="text-sm text-gray-600">
                  Full policy available at:{" "}
                  <a href={policy.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                    {policy.url}
                  </a>
                </p>
              </div>
            )}

            {policy.lastUpdated && (
              <div className="mt-2">
                <p className="text-xs text-gray-500">Last updated: {new Date(policy.lastUpdated).toLocaleDateString()}</p>
              </div>
            )}
          </div>
        </div>

        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <button
            onClick={onClose}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export const CompliancePoliciesDisplay = ({ policies, termsAccepted, onTermsChange }: CompliancePoliciesDisplayProps) => {
  const [openPolicy, setOpenPolicy] = useState<URLOnlyPolicy | null>(null);

  const handlePolicyClick = (policy: URLOnlyPolicy) => {
    // Return/refund policy always opens URL, never shows modal
    if (policy.type === "return_refund") {
      if (policy.url) {
        window.open(policy.url, "_blank", "noopener,noreferrer");
      }
      return;
    }

    // For other policies, show modal if no URL, otherwise open URL
    if (policy.url) {
      window.open(policy.url, "_blank", "noopener,noreferrer");
    } else {
      setOpenPolicy(policy);
    }
  };

  const renderPolicyLink = (policy: URLOnlyPolicy, text: string) => (
    <button
      type="button"
      onClick={() => handlePolicyClick(policy)}
      className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
    >
      {text}
    </button>
  );

  return (
    <>
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">E-commerce Compliance Information</h4>
        <div className="text-xs text-blue-800 space-y-1">
          <p>• Your payment is secured with industry-standard encryption</p>
          <p>• All transactions are processed through PCI-compliant systems</p>
          <p>• Your personal information is protected according to our privacy policy</p>
        </div>
      </div>

      <div className="space-y-3 mb-4">
        <div className="text-xs text-gray-600 space-y-2">
          <div className="flex flex-wrap items-center gap-1">
            <span>📋</span>
            <span>Review our</span>
            {renderPolicyLink(policies.returnRefundPolicy, "return & refund policy")}
            {policies.deliveryPolicy && (
              <>
                <span>and</span>
                {renderPolicyLink(policies.deliveryPolicy, "delivery policy")}
              </>
            )}
          </div>

          <div className="flex flex-wrap items-center gap-1">
            <span>🔒</span>
            <span>Your data is protected by our</span>
            {renderPolicyLink(policies.privacyPolicy, "privacy policy")}
            <span>and</span>
            {renderPolicyLink(policies.securityPolicy, "security policy")}
          </div>
        </div>
      </div>

      <div className="border-t border-gray-100 pt-3">
        <div className="flex items-start space-x-2 min-h-[44px]">
          <input
            type="checkbox"
            id="compliance-terms"
            checked={termsAccepted}
            onChange={(e) => onTermsChange(e.target.checked)}
            className="mt-0.5 h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
            aria-describedby="compliance-terms-description"
          />
          <label
            htmlFor="compliance-terms"
            className="flex-1 py-2 cursor-pointer text-xs text-gray-700 leading-relaxed"
            id="compliance-terms-description"
          >
            I acknowledge that I have reviewed and agree to the merchant&apos;s{" "}
            {renderPolicyLink(policies.termsAndConditions, "terms and conditions")}, {renderPolicyLink(policies.returnRefundPolicy, "return policy")},{" "}
            {renderPolicyLink(policies.privacyPolicy, "privacy policy")}, and {renderPolicyLink(policies.securityPolicy, "security practices")}.
            {policies.deliveryPolicy && <> I also acknowledge the {renderPolicyLink(policies.deliveryPolicy, "delivery policy")}.</>}
          </label>
        </div>
      </div>

      {openPolicy && <PolicyModal policy={openPolicy} isOpen={true} onClose={() => setOpenPolicy(null)} />}
    </>
  );
};
