import { PrimaryColor } from "../../../constants/colors";
import { LoadingSpinner } from "../../common/LoadingSpinner";

export const LoadingState = () => {
  return (
    <div className="min-h-screen bg-slate-50 flex items-center justify-center">
      <div className="text-center py-12">
        <LoadingSpinner 
          size="large" 
          color="custom" 
          customColor={PrimaryColor.hex}
          text="Loading payment form..."
          textColor={PrimaryColor.hex}
        />
      </div>
    </div>
  );
};