import { AlertCircle, ShieldCheck } from "lucide-react";

interface PaymentValidationProps {
  error: string | null;
  isAddressValid: boolean;
}

export const PaymentValidation = ({ error, isAddressValid }: PaymentValidationProps) => {
  return (
    <>
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-start gap-2">
          <AlertCircle className="h-4 w-4 text-red-600 mt-0.5" />
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}

      {!isAddressValid && (
        <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md flex items-start gap-2">
          <ShieldCheck className="h-4 w-4 text-amber-700 mt-0.5" />
          <p className="text-sm text-amber-700">Please complete billing information and accept terms to continue</p>
        </div>
      )}
    </>
  );
};
