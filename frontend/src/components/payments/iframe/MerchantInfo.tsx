import type { MerchantInfo as MerchantInfoType } from "../../../types/payment";

interface MerchantInfoProps {
  merchantInfo: MerchantInfoType;
}

export const MerchantInfo = ({ merchantInfo }: MerchantInfoProps) => {
  const { dba, name, address, contactEmail, contactPhone } = merchantInfo;
  const displayName = dba || name;

  return (
    <div className="space-y-4">
      <div>
        <p className="text-sm text-gray-500">Merchant Information</p>
        <p className="font-medium text-gray-900">{displayName}</p>

        {address && (address.line1 || address.city) && (
          <div className="text-sm text-gray-600 mt-2">
            <p className="font-medium text-gray-700 mb-1">Business Address:</p>
            {address.line1 && <p>{address.line1}</p>}
            {address.line2 && <p>{address.line2}</p>}
            {(address.city || address.state || address.zip) && (
              <p>{[address.city, address.state, address.zip].filter(Boolean).join(", ")}</p>
            )}
          </div>
        )}

        {(contactEmail || contactPhone) && (
          <div className="text-sm text-gray-600 mt-3">
            <p className="font-medium text-gray-700 mb-1">Customer Service Contact:</p>
            {contactEmail && (
              <p>
                Email:{" "}
                <a href={`mailto:${contactEmail}`} className="text-blue-600 hover:text-blue-800 underline">
                  {contactEmail}
                </a>
              </p>
            )}
            {contactPhone && <p>Phone: {contactPhone}</p>}
          </div>
        )}
      </div>
    </div>
  );
};
