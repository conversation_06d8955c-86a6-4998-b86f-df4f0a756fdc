import type { MerchantInfo as MerchantInfoType, PaymentInfo } from "../../../types/payment";
import { OrderOverview } from "./OrderOverview";
import { MerchantInfo } from "./MerchantInfo";
import { PoweredBySection } from "./PoweredBySection";
import { PrimaryColor } from "../../../constants/colors";

interface OrderSummaryProps {
  paymentInfo: PaymentInfo | null;
  merchantInfo: MerchantInfoType | null;
}

export const OrderSummary = ({ paymentInfo, merchantInfo }: OrderSummaryProps) => {
  return (
    <div className="h-full flex flex-col space-y-4">
      <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 shadow-sm flex-1 flex flex-col">
        <h3
          className="text-base sm:text-lg font-medium mb-2 sm:mb-3"
          style={{ color: PrimaryColor.hex }}
        >
          Order Summary
        </h3>

        {/* Order Overview */}
        <div className="flex-1">
          {paymentInfo && <OrderOverview paymentInfo={paymentInfo} />}

          {/* Merchant Information - Progressive disclosure */}
          {merchantInfo && (
            <div className="mt-3 pt-2 border-t border-gray-100">
              <MerchantInfo merchantInfo={merchantInfo} />
            </div>
          )}
        </div>
      </div>

      {/* Powered by section - Minimized */}
      <PoweredBySection />
    </div>
  );
};
