import type { BillingAddress, CompliancePolicies } from "../../../types/payment";
import { AddressFields } from "./AddressFields";
import { PrimaryColor } from "../../../constants/colors";
import { PaymentValidation } from "./PaymentValidation";
import { CompliancePoliciesDisplay } from "./CompliancePoliciesDisplay";
import { BasicTermsDisplay } from "./BasicTermsDisplay";

interface BillingAddressFormProps {
  billingAddress: BillingAddress;
  termsAccepted: boolean;
  handleAddressChange: (field: keyof BillingAddress, value: string) => void;
  setTermsAccepted: (accepted: boolean) => void;
  error: string | null;
  isAddressValid: boolean;
  compliancePolicies?: CompliancePolicies;
}

export const BillingAddressForm = ({
  billingAddress,
  termsAccepted,
  handleAddressChange,
  setTermsAccepted,
  error,
  isAddressValid,
  compliancePolicies,
}: BillingAddressFormProps) => {
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 shadow-sm h-full flex flex-col">
      <h3 className="text-base sm:text-lg font-medium mb-2 sm:mb-3" style={{ color: PrimaryColor.hex }}>
        Billing Address
      </h3>

      <AddressFields billingAddress={billingAddress} handleAddressChange={handleAddressChange} />

      {/* Terms and Compliance Policies */}
      <div className="pt-3 border-t border-gray-100">
        {compliancePolicies ? (
          <CompliancePoliciesDisplay policies={compliancePolicies} termsAccepted={termsAccepted} onTermsChange={setTermsAccepted} />
        ) : (
          <BasicTermsDisplay termsAccepted={termsAccepted} onTermsChange={setTermsAccepted} />
        )}
      </div>

      <div className="mb-4">
        <PaymentValidation error={error} isAddressValid={isAddressValid} />
      </div>

      {/* <SecurityInfoSection /> */}
    </div>
  );
};
