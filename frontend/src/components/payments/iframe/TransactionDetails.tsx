import type { PaymentInfo } from "../../../types/payment";
import { formatCurrency, formatDate } from "../../../utils/paymentUtils";

interface TransactionDetailsProps {
  paymentInfo: PaymentInfo;
}

export const TransactionDetails = ({ paymentInfo }: TransactionDetailsProps) => {
  // const [showBreakdown, setShowBreakdown] = useState(false);

  // Generate reference number and date

  const date = formatDate();

  // Format amounts
  const formattedAmount = formatCurrency(paymentInfo.amount, paymentInfo.currency);
  // const taxAmount = paymentInfo.taxAmount || 0;
  // const subtotal = calculateSubtotal(paymentInfo.amount, taxAmount);
  // const formattedSubtotal = formatCurrency(subtotal, paymentInfo.currency);
  // const formattedTax = formatCurrency(taxAmount, paymentInfo.currency);

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-3 sm:p-4 shadow-sm">
      {/* Compact header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center">
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-900">Transaction Details</h3>
            <p className="text-xs text-gray-500">{date}</p>
          </div>
        </div>
        <div className="text-right">
          <p className="text-lg font-bold text-gray-900">{formattedAmount}</p>
        </div>
      </div>

      {/* Description */}
      <div className="mb-3">
        <p className="text-sm text-gray-700 leading-relaxed">{paymentInfo.description}</p>
      </div>

      {/* Expandable breakdown */}
      {/* <button
        onClick={() => setShowBreakdown(!showBreakdown)}
        aria-expanded={showBreakdown}
        aria-controls="transaction-breakdown"
        className="flex items-center justify-center w-full py-3 px-3 min-h-[44px] bg-white hover:bg-gray-50 rounded border border-gray-200 transition-colors text-xs font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        <svg
          className={`w-3 h-3 mr-1 transition-transform ${showBreakdown ? "rotate-180" : ""}`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          aria-hidden="true"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
        </svg>
        {showBreakdown ? "Hide Details" : "Show Details"}
      </button> */}

      {/* {showBreakdown && (
        <div id="transaction-breakdown" className="mt-3 p-3 bg-white rounded border border-gray-200">
          <div className="space-y-2 text-xs">
            {paymentInfo.items && paymentInfo.items.length > 0 ? (
              <>
                {paymentInfo.items.map((item, index) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-gray-600">
                      {item.name} ({item.quantity}x)
                    </span>
                    <span className="text-gray-900">{formatCurrency(item.total, paymentInfo.currency)}</span>
                  </div>
                ))}
                <div className="flex justify-between border-t border-gray-200 pt-2">
                  <span className="text-gray-600">Subtotal</span>
                  <span className="text-gray-900">{formattedSubtotal}</span>
                </div>
              </>
            ) : (
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal</span>
                <span className="text-gray-900">{formattedSubtotal}</span>
              </div>
            )}
            {taxAmount > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-600">Tax</span>
                <span className="text-gray-900">{formattedTax}</span>
              </div>
            )}
            <div className="flex justify-between border-t border-gray-200 pt-2 font-medium">
              <span className="text-gray-900">Total</span>
              <span className="text-gray-900">{formattedAmount}</span>
            </div>
          </div>
        </div>
      )} */}
    </div>
  );
};
