interface BasicTermsDisplayProps {
  termsAccepted: boolean;
  onTermsChange: (accepted: boolean) => void;
}

export const BasicTermsDisplay = ({ termsAccepted, onTermsChange }: BasicTermsDisplayProps) => {
  return (
    <div className="border-t border-gray-100 pt-3">
      <div className="flex items-start space-x-2 min-h-[44px]">
        <input
          type="checkbox"
          id="basic-terms"
          checked={termsAccepted}
          onChange={(e) => onTermsChange(e.target.checked)}
          className="mt-0.5 h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
          aria-describedby="basic-terms-description"
        />
        <label 
          htmlFor="basic-terms" 
          className="flex-1 py-2 cursor-pointer text-xs text-gray-700 leading-relaxed" 
          id="basic-terms-description"
        >
          I agree to the{" "}
          <button
            type="button"
            className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
            onClick={() => {}}
          >
            terms
          </button>
          {", "}
          <button
            type="button"
            className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
            onClick={() => {}}
          >
            privacy policy
          </button>
          {", and "}
          <button
            type="button"
            className="text-blue-600 hover:underline focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
            onClick={() => {}}
          >
            refund policy
          </button>
          .
        </label>
      </div>
    </div>
  );
};
