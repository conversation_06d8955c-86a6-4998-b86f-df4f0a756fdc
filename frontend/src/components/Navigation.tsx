import { Link, useLocation } from "react-router-dom";

export const Navigation = () => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + "/");
  };

  const navLinkClass = (path: string, highlight?: boolean) => {
    const base = "px-4 py-2 rounded-xl font-medium transition-all duration-300 flex items-center space-x-2";
    const active = "bg-blue-100 text-blue-700 shadow-sm";
    const inactive = "text-slate-600 hover:text-blue-600 hover:bg-blue-50";
    const highlighted = highlight ? "ring-2 ring-blue-200" : "";
    return `${base} ${isActive(path) ? active : inactive} ${highlighted}`;
  };

  const navLinks = [
    { to: "/", label: "Home" },
    { to: "/iframe-demo", label: "Iframe Demo" },
  ];

  return (
    <nav className="bg-white/90 backdrop-blur-md shadow-lg border-b border-blue-100 fixed top-0 left-0 right-0 z-50 w-full">
      <div className="w-full px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity duration-300">
            <img src="/LogoFiles/svg/Black logo - no background.svg" alt="Auth-Clear" className="h-8 w-auto" />
          </Link>

          <div className="hidden md:flex items-center space-x-4 flex-1 justify-end mr-8">
            {navLinks.map(({ to, label }) => (
              <Link key={to} to={to} className={navLinkClass(to)}>
                <span>{label}</span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </nav>
  );
};
