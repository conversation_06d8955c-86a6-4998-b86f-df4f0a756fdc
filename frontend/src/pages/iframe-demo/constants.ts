export const DEFAULT_DEMO_CONFIG = {
  merchantId: "t1_mer_68c082d3cad7efa04eef2e5",
  description: "Demo Product Purchase",
  amount: 5000,
  returnUrl: "https://example.com/success",
  enableGooglePay: true,
  googlePayEnvironment: "TEST" as const,
  googlePayMerchantName: "Auth-Clear Demo",
};

export const DEMO_SECTIONS = [
  { id: "configuration", title: "Payment Configuration" },
  { id: "iframe-demo", title: "Payment Iframe" },
  { id: "event-log", title: "Event Log" },
  { id: "integration-url", title: "Integration URL" },
];

export const EVENT_TYPES = {
  IFRAME_READY: "PAYMENT_IFRAME_READY",
  SUCCESS: "PAYMENT_SUCCESS",
  FAILURE: "PAYMENT_FAILURE",
  VALIDATION_FAILURE: "PAYMENT_VALIDATION_FAILURE",
  TIMEOUT: "PAYMENT_TIMEOUT",
} as const;

export const MAX_EVENTS = 10;
