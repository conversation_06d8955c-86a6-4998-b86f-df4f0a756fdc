import { CompliancePolicies, MerchantPolicy } from "../../../types/payment";

interface CompliancePoliciesFormProps {
  policies: CompliancePolicies | undefined;
  setPolicies: (policies: CompliancePolicies | undefined) => void;
}

const defaultPolicies: CompliancePolicies = {
  returnRefundPolicy: {
    type: "return_refund",
    title: "Return and Refund Policy",
    content: "We offer a 30-day return policy for all items. Items must be in original condition. Refunds will be processed within 5-7 business days after we receive the returned item. Shipping costs are non-refundable unless the item was defective or incorrectly shipped.",
    url: "https://example.com/return-policy",
    version: "1.0",
    lastUpdated: new Date().toISOString().split('T')[0]
  },
  deliveryPolicy: {
    type: "delivery",
    title: "Delivery Policy", 
    content: "Standard shipping takes 3-5 business days. Express shipping available for next-day delivery. We ship Monday through Friday. Delivery confirmation required for orders over $100. International shipping available to select countries with additional fees.",
    url: "https://example.com/delivery-policy",
    version: "1.0",
    lastUpdated: new Date().toISOString().split('T')[0]
  },
  privacyPolicy: {
    type: "privacy",
    title: "Privacy Policy",
    content: "We collect and protect your personal information in accordance with applicable privacy laws. Your payment information is encrypted and securely processed. We do not sell or share your personal data with third parties except as necessary to complete your transaction.",
    url: "https://example.com/privacy-policy", 
    version: "1.0",
    lastUpdated: new Date().toISOString().split('T')[0]
  },
  securityPolicy: {
    type: "security",
    title: "Secure Checkout Policy",
    content: "Our checkout process uses industry-standard SSL encryption to protect your payment information. All transactions are processed through PCI-compliant payment processors. Your credit card information is never stored on our servers.",
    url: "https://example.com/security-policy",
    version: "1.0", 
    lastUpdated: new Date().toISOString().split('T')[0]
  },
  termsAndConditions: {
    type: "terms",
    title: "Terms and Conditions",
    content: "By making a purchase, you agree to our terms of service. All sales are final unless covered by our return policy. We reserve the right to refuse service. Prices are subject to change without notice. These terms are governed by applicable local laws.",
    url: "https://example.com/terms",
    version: "1.0",
    lastUpdated: new Date().toISOString().split('T')[0]
  }
};

export const CompliancePoliciesForm = ({ policies, setPolicies }: CompliancePoliciesFormProps) => {
  const handleTogglePolicies = (enabled: boolean) => {
    if (enabled) {
      setPolicies(defaultPolicies);
    } else {
      setPolicies(undefined);
    }
  };

  const handlePolicyChange = (policyKey: keyof CompliancePolicies, field: keyof MerchantPolicy, value: string) => {
    if (!policies) return;
    
    setPolicies({
      ...policies,
      [policyKey]: {
        ...policies[policyKey],
        [field]: value
      }
    });
  };

  const renderPolicyField = (
    policyKey: keyof CompliancePolicies, 
    policy: MerchantPolicy | undefined,
    label: string,
    required: boolean = true
  ) => {
    if (!policy) return null;

    return (
      <div className="space-y-3 p-4 border border-slate-200 rounded-lg bg-slate-50">
        <h4 className="font-medium text-slate-900">{label} {required && <span className="text-red-500">*</span>}</h4>
        
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-1">Title</label>
          <input
            type="text"
            value={policy.title}
            onChange={(e) => handlePolicyChange(policyKey, 'title', e.target.value)}
            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 text-sm"
            placeholder="Policy title"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-1">Content</label>
          <textarea
            value={policy.content}
            onChange={(e) => handlePolicyChange(policyKey, 'content', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 text-sm"
            placeholder="Policy content"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-slate-700 mb-1">URL (Optional)</label>
          <input
            type="url"
            value={policy.url || ''}
            onChange={(e) => handlePolicyChange(policyKey, 'url', e.target.value)}
            className="w-full px-3 py-2 border border-slate-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-slate-500 text-sm"
            placeholder="https://example.com/policy"
          />
        </div>
      </div>
    );
  };

  return (
    <div className="border-t border-slate-200 pt-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-slate-900">Compliance Policies</h3>
        <div className="flex items-center">
          <input
            type="checkbox"
            id="enablePolicies"
            checked={!!policies}
            onChange={(e) => handleTogglePolicies(e.target.checked)}
            className="h-4 w-4 text-slate-600 focus:ring-slate-500 border-gray-300 rounded"
          />
          <label htmlFor="enablePolicies" className="ml-2 block text-sm text-slate-700">
            Enable Payrix E-commerce Compliance
          </label>
        </div>
      </div>

      {policies && (
        <div className="space-y-4">
          <p className="text-sm text-slate-600 mb-4">
            Configure the required compliance policies for Payrix e-commerce integration. These policies will be displayed during checkout.
          </p>
          
          {renderPolicyField('returnRefundPolicy', policies.returnRefundPolicy, 'Return & Refund Policy', true)}
          {renderPolicyField('deliveryPolicy', policies.deliveryPolicy, 'Delivery Policy', false)}
          {renderPolicyField('privacyPolicy', policies.privacyPolicy, 'Privacy Policy', true)}
          {renderPolicyField('securityPolicy', policies.securityPolicy, 'Security Policy', true)}
          {renderPolicyField('termsAndConditions', policies.termsAndConditions, 'Terms & Conditions', true)}
        </div>
      )}
    </div>
  );
};
