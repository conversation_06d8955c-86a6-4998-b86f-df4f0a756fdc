import { usePaymentIframe } from "../hooks/usePaymentIframe";
import { useBillingAddress } from "../hooks/useBillingAddress";
import { usePaymentHandlers } from "../hooks/usePaymentHandlers";

import { TransactionDetails } from "../components/payments/iframe/TransactionDetails";
import { OrderSummary } from "../components/payments/iframe/OrderSummary";
import { BillingAddressForm } from "../components/payments/iframe/BillingAddressForm";
import { PaymentFormSection } from "../components/payments/iframe/PaymentFormSection";
import { PaymentLayout } from "../components/layouts/PaymentLayout";
import { LoadingState } from "../components/payments/iframe/LoadingState";
import { ErrorState } from "../components/payments/iframe/ErrorState";
import { SuccessState } from "../components/payments/iframe/SuccessState";
import { ErrorMessage } from "../components/common/ErrorMessage";

const PaymentIframe = () => {
  const { payFieldsConfig, merchantInfo, paymentInfo, error: iframeError, loading } = usePaymentIframe();
  const { billingAddress, termsAccepted, handleAddressChange, setTermsAccepted, isAddressValid } = useBillingAddress();
  const { success, error: paymentError, handlePaymentSuccess, handlePaymentFailure } = usePaymentHandlers();

  const error = iframeError || paymentError;

  if (loading) {
    return <LoadingState />;
  }

  if (error && !payFieldsConfig) {
    return <ErrorState error={error} />;
  }

  if (success) {
    return <SuccessState paymentInfo={paymentInfo} />;
  }

  return (
    <PaymentLayout>
      {/* Error Messages */}
      {error && !success && (
        <ErrorMessage message={error} variant="banner" />
      )}

      {!success && (
        <div className="flex flex-col h-full">
          {/* Transaction Details */}
          <div className="flex-shrink-0 mb-4">{paymentInfo && <TransactionDetails paymentInfo={paymentInfo} />}</div>

          {/* Main Form Section with 3 columns */}
          <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:items-start">
            {/* Transaction Summary Column */}
            <div className="order-1 lg:order-1 lg:col-span-1 flex flex-col h-full">
              <div className="flex-1">
                <OrderSummary paymentInfo={paymentInfo} merchantInfo={merchantInfo} />
              </div>
            </div>

            {/* Address Form Column */}
            <div className="order-2 lg:order-2 lg:col-span-1 flex flex-col h-full">
              <div className="flex-1">
                <BillingAddressForm
                  billingAddress={billingAddress}
                  termsAccepted={termsAccepted}
                  handleAddressChange={handleAddressChange}
                  setTermsAccepted={setTermsAccepted}
                  error={error}
                  isAddressValid={isAddressValid()}
                  compliancePolicies={paymentInfo?.compliancePolicies}
                />
              </div>
            </div>

            {/* Payment Form Column */}
            <div className="order-3 lg:order-3 lg:col-span-1 flex flex-col h-full">
              <div className="flex-1">
                <PaymentFormSection
                  payFieldsConfig={payFieldsConfig}
                  paymentInfo={paymentInfo}
                  billingAddress={billingAddress}
                  error={error}
                  isAddressValid={isAddressValid()}
                  loading={loading}
                  onSuccess={handlePaymentSuccess}
                  onFailure={handlePaymentFailure}
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </PaymentLayout>
  );
};

export default PaymentIframe;
