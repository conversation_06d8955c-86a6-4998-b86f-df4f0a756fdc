import React from "react";
import { useSelector } from "react-redux";
import { type RootState } from "../redux/store.ts";
import BusinessInfoForm from "../components/onboarding/BusinessInfoForm.tsx";
import BankAccountForm from "../components/onboarding/BankAccountForm.tsx";
import OwnerInfoFormMultiple from "../components/onboarding/OwnerInfoFormMultiple.tsx";
import PolicyCollectionForm from "../components/onboarding/PolicyCollectionForm.tsx";
import OnboardingReview from "../components/onboarding/OnboardingReview.tsx";

const OnboardingPage: React.FC = () => {
  const { step } = useSelector((state: RootState) => state.onboarding);

  const renderStep = () => {
    switch (step) {
      case 1:
        return <BusinessInfoForm />;
      case 2:
        return <OwnerInfoFormMultiple />;
      case 3:
        return <BankAccountForm />;
      case 4:
        return <PolicyCollectionForm />;
      case 5:
        return <OnboardingReview />;
      default:
        return <div>Unknown Step</div>;
    }
  };

  return <div className="mx-auto">{renderStep()}</div>;
};

export default OnboardingPage;
