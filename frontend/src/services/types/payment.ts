export interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
  returnUrl?: string;
}

export interface GooglePayConfig {
  enabled: boolean;
  merchantName: string;
  environment: "TEST" | "PRODUCTION";
  allowedCardNetworks: string[];
  allowedCardAuthMethods: string[];
  billingAddressRequired: boolean;
  phoneNumberRequired: boolean;
}

// All policies are now URL-only with optional content
export interface URLOnlyPolicy {
  type: "return_refund" | "delivery" | "privacy" | "security" | "terms";
  title: string;
  content?: string; // Optional for all policies (URL-only)
  url: string; // Required for all policies
  lastUpdated?: string;
  version?: string;
}

export interface CompliancePolicies {
  returnRefundPolicy: URLOnlyPolicy;
  deliveryPolicy?: URLOnlyPolicy;
  privacyPolicy: URLOnlyPolicy;
  securityPolicy: URLOnlyPolicy;
  termsAndConditions: URLOnlyPolicy;
}

export interface GenerateIntegrationTokenRequest {
  merchantId: string;
  description: string;
  amount?: number;
  returnUrl?: string;
  expiresIn?: number;
  currency?: string;
  items?: Array<{
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    total: number;
    commodityCode?: string;
    productCode?: string;
  }>;
  taxAmount?: number;
  shippingAmount?: number;
  dutyAmount?: number;
  orderNumber?: string;
  invoiceNumber?: string;
  customerCode?: string;
  orderDiscount?: number;
  googlePayConfig?: GooglePayConfig;
  enableDigitalWallets?: boolean;
  // compliancePolicies removed - now retrieved from DynamoDB during token generation
}

export interface GenerateIntegrationTokenResponse {
  success: boolean;
  message: string;
  data: {
    token: string;
    expiresAt: string;
    embedUrl: string;
    merchantInfo: {
      id: string;
      name: string;
      status: number;
    };
  };
}

export interface ValidateIframeTokenResponse {
  success: boolean;
  message: string;
  data?: {
    config: PayFieldsConfig;
    merchantInfo: {
      id: string;
      name: string;
      status: number;
    };
    paymentInfo: {
      description: string;
      amount: number;
      returnUrl?: string;
      compliancePolicies?: CompliancePolicies;
    };
  };
}

export interface TokenPaymentRequest {
  merchantId: string;
  token: string;
  tokenId?: string;
  amount: number;
  description?: string;
  paymentType?: "CARD" | "GOOGLE_PAY" | "APPLE_PAY";
  customerInfo?: {
    email?: string;
    name?: string;
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
  };
}

export interface TokenPaymentResponse {
  success: boolean;
  message: string;
  transaction?: {
    id: string;
    status: string;
    amount: number;
    merchantId: string;
    description: string;
    createdAt: string;
  };
  merchantInfo?: {
    id: string;
    name: string;
    status: number;
  };
  error?: string;
}
