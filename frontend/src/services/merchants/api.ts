import type { AxiosResponse } from "axios";
import { apiClient } from "../config";
import type {
  CreatePayrixMerchantRequest,
  MerchantResponse,
  CreateNoteRequest,
  CreateNoteResponse,
  CreateNoteDocumentRequest,
  CreateNoteDocumentResponse,
  CreatePlaidLinkTokenRequest,
  CreatePlaidLinkTokenResponse,
  ProcessPlaidAccountRequest,
  ProcessPlaidAccountResponse,
} from "../types/merchant";

export const createMerchant = async (merchantData: CreatePayrixMerchantRequest): Promise<MerchantResponse> => {
  const response: AxiosResponse<MerchantResponse> = await apiClient.post("/merchants/onboard", merchantData);
  return response.data;
};

export const createNote = async (noteData: CreateNoteRequest): Promise<CreateNoteResponse> => {
  const response: AxiosResponse<CreateNoteResponse> = await apiClient.post("/merchants/notes", noteData);
  return response.data;
};

export const createNoteDocument = async (documentData: CreateNoteDocumentRequest): Promise<CreateNoteDocumentResponse> => {
  const formData = new FormData();
  formData.append("noteId", documentData.noteId);
  formData.append("file", documentData.file);
  if (documentData.description) {
    formData.append("description", documentData.description);
  }

  const response: AxiosResponse<CreateNoteDocumentResponse> = await apiClient.post("/merchants/note-documents", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

export const createPlaidLinkToken = async (tokenData: CreatePlaidLinkTokenRequest): Promise<CreatePlaidLinkTokenResponse> => {
  const response: AxiosResponse<CreatePlaidLinkTokenResponse> = await apiClient.post("/merchants/plaid/link-token", tokenData);
  return response.data;
};

export const processPlaidAccount = async (accountData: ProcessPlaidAccountRequest): Promise<ProcessPlaidAccountResponse> => {
  const response: AxiosResponse<ProcessPlaidAccountResponse> = await apiClient.post("/merchants/plaid/process-account", accountData);
  return response.data;
};
