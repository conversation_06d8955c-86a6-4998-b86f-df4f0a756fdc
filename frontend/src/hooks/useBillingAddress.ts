import { useState } from "react";
import type { BillingAddress } from "../types/payment";
import { validateBillingAddress } from "../utils/paymentUtils";

interface UseBillingAddressReturn {
  billingAddress: BillingAddress;
  termsAccepted: boolean;
  setBillingAddress: React.Dispatch<React.SetStateAction<BillingAddress>>;
  setTermsAccepted: React.Dispatch<React.SetStateAction<boolean>>;
  handleAddressChange: (field: keyof BillingAddress, value: string) => void;
  isAddressValid: () => boolean;
}

/**
 * Custom hook for managing billing address state and validation
 */
export const useBillingAddress = (): UseBillingAddressReturn => {
  const [billingAddress, setBillingAddress] = useState<BillingAddress>({
    firstName: "test",
    lastName: "test",
    email: "<EMAIL>",
    phone: "1234567890",
    line1: "123 Main St",
    line2: "Apt 1",
    city: "New York",
    state: "NY",
    zip: "10001",
    country: "USA",
  });

  const [termsAccepted, setTermsAccepted] = useState(false);

  const handleAddressChange = (field: keyof BillingAddress, value: string) => {
    setBillingAddress((prev) => ({ ...prev, [field]: value }));
  };

  const isAddressValid = () => {
    return validateBillingAddress(billingAddress, termsAccepted);
  };

  return {
    billingAddress,
    termsAccepted,
    setBillingAddress,
    setTermsAccepted,
    handleAddressChange,
    isAddressValid,
  };
};
