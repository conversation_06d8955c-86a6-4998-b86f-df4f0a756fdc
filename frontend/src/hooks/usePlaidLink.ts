import { useState, useCallback } from "react";
import { usePlaidLink, PlaidLinkOptions, PlaidLinkOnSuccess, PlaidLinkOnExit } from "react-plaid-link";
import { toast } from "sonner";
import { createPlaidLinkToken, processPlaidAccount } from "../services/merchants/api";

export interface PlaidAccountData {
  publicToken: string;
  accountToken: string;
  platform: "PLAID";
  institutionName?: string;
  accountName?: string;
  accountMask?: string;
  accountType?: string;
  accountSubtype?: string;
  verificationStatus?: string | null;
}

interface UsePlaidLinkProps {
  userId: string;
  countryCode?: "US" | "CA";
  redirectUri: string;
  onSuccess: (accountData: PlaidAccountData) => void;
  onError?: (error: string) => void;
}

interface UsePlaidLinkReturn {
  open: (() => void) | null;
  ready: boolean;
  error: string | null;
  loading: boolean;
  initializePlaid: () => Promise<void>;
}

export const usePlaidLinkIntegration = ({ userId, countryCode = "US", redirectUri, onSuccess, onError }: UsePlaidLinkProps): UsePlaidLinkReturn => {
  // linkToken to store the Plaid link token for initialization
  const [linkToken, setLinkToken] = useState<string | null>(null);
  // loading to track API call states
  const [loading, setLoading] = useState(false);
  // error to store any error messages
  const [error, setError] = useState<string | null>(null);

  // initializePlaid to create Plaid link token and setup connection
  const initializePlaid = useCallback(async () => {
    if (linkToken) return; // Already initialized

    setLoading(true);
    setError(null);

    try {
      const response = await createPlaidLinkToken({
        userId,
        countryCode,
        redirectUri,
      });

      if (response.success && response.data.linkToken) {
        setLinkToken(response.data.linkToken);
        toast.success("Plaid Link initialized successfully");
      } else {
        throw new Error(response.message || "Failed to create Plaid link token");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to initialize Plaid Link";
      setError(errorMessage);
      onError?.(errorMessage);
      toast.error("Failed to initialize bank verification", {
        description: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  }, [userId, countryCode, redirectUri, linkToken, onError]);

  // handleOnSuccess to process successful Plaid link connection and account data
  const handleOnSuccess: PlaidLinkOnSuccess = useCallback(
    async (public_token, metadata) => {
      setLoading(true);

      try {
        // Get the first account from the accounts array
        const account = metadata.accounts[0];

        const response = await processPlaidAccount({
          publicToken: public_token,
          accountId: account.id,
          metadata: {
            institution: metadata.institution || { name: "Unknown Bank", institution_id: "unknown" },
            account: {
              id: account.id,
              name: account.name,
              mask: account.mask,
              type: account.type,
              subtype: account.subtype,
              verification_status: account.verification_status,
            },
            link_session_id: metadata.link_session_id,
          },
        });

        if (response.success) {
          const accountData: PlaidAccountData = {
            publicToken: response.data.publicToken,
            accountToken: response.data.accountToken,
            platform: "PLAID",
            institutionName: response.data.institutionName,
            accountName: response.data.accountName,
            accountMask: response.data.accountMask,
            accountType: response.data.accountType,
            accountSubtype: response.data.accountSubtype,
            verificationStatus: response.data.verificationStatus,
          };

          onSuccess(accountData);
          toast.success("Bank account verified successfully", {
            description: `Connected ${accountData.institutionName} account ending in ${accountData.accountMask}`,
          });
        } else {
          throw new Error(response.message || "Failed to process bank account");
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to process bank account";
        setError(errorMessage);
        onError?.(errorMessage);
        toast.error("Bank verification failed", {
          description: errorMessage,
        });
      } finally {
        setLoading(false);
      }
    },
    [onSuccess, onError]
  );

  // handleOnExit to handle Plaid link exit events and errors
  const handleOnExit: PlaidLinkOnExit = useCallback(
    (err) => {
      if (err != null) {
        const errorMessage = `Plaid Link error: ${err.error_message || err.error_code}`;
        setError(errorMessage);
        onError?.(errorMessage);
        toast.error("Bank verification cancelled", {
          description: "Please try again or use manual verification",
        });
      } else {
        // User exited without error (cancelled)
        toast.info("Bank verification cancelled");
      }
    },
    [onError]
  );

  // config to setup Plaid link with token and event handlers
  const config: PlaidLinkOptions = {
    token: linkToken,
    onSuccess: handleOnSuccess,
    onExit: handleOnExit,
  };

  // usePlaidLink hook to get open function and ready state
  const { open, ready } = usePlaidLink(config);

  // return hook interface with Plaid link functions and state
  return {
    open: linkToken ? (open as () => void) : null,
    ready: ready && !!linkToken,
    error,
    loading,
    initializePlaid,
  };
};
