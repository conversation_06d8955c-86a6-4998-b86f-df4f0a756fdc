export const CURL_EXAMPLE = `curl -X POST https://your-api-domain.com/payments/generate-integration-token \\
  -H "Content-Type: application/json" \\
  -d '{
    "merchantId": "your-merchant-id",
    "description": "Product Purchase",
    "amount": 2500,
    "returnUrl": "https://yoursite.com/success"
  }'`;

export const IFRAME_EMBED_EXAMPLE = `<iframe
  src="https://your-domain.com/payment-iframe?token=..."
  width="100%"
  height="600"
  frameborder="0"
  allow="payment">
</iframe>`;

export const EVENT_LISTENER_EXAMPLE = `window.addEventListener('message', (event) => {
  if (event.data.type === 'PAYMENT_SUCCESS') {
    console.log('Payment successful!', event.data.data);
    // Handle success
  } else if (event.data.type === 'PAYMENT_FAILURE') {
    console.log('Payment failed:', event.data.error);
    // Handle failure
  }
});`;