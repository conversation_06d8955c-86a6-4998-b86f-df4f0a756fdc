export const REACT_EXAMPLE = `// Modern JavaScript/React Integration Example
import { useEffect, useRef, useState } from 'react';

const PaymentIntegration = ({ merchantId, description, amount, returnUrl }) => {
  const iframeRef = useRef(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadPaymentIframe();
    
    // Listen for payment events
    const handleMessage = (event) => {
      // Verify origin for security
      if (event.origin !== 'https://your-payment-domain.com') return;

      switch (event.data.type) {
        case 'PAYMENT_IFRAME_READY':
          setLoading(false);
          break;
        case 'PAYMENT_SUCCESS':
          onPaymentSuccess(event.data.data);
          break;
        case 'PAYMENT_FAILURE':
          onPaymentFailure(event.data.error);
          break;
        case 'PAYMENT_REDIRECT':
          window.location.href = event.data.url;
          break;
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  const loadPaymentIframe = async () => {
    try {
      const response = await fetch('/api/payments/generate-integration-token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ merchantId, description, amount, returnUrl })
      });

      const result = await response.json();
      if (!result.success) throw new Error(result.message);

      if (iframeRef.current) {
        iframeRef.current.src = result.data.embedUrl;
      }
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  const onPaymentSuccess = (data) => {
    console.log('Payment successful:', data);
    // Handle success
  };

  const onPaymentFailure = (error) => {
    console.error('Payment failed:', error);
    setError(error);
  };

  if (error) {
    return <div className="error">Payment Error: {error}</div>;
  }

  return (
    <div className="payment-container">
      {loading && <div className="loading">Loading payment form...</div>}
      <iframe
        ref={iframeRef}
        className="payment-iframe"
        style={{ 
          width: '100%', 
          height: '600px', 
          border: 'none',
          display: loading ? 'none' : 'block'
        }}
        allow="payment"
        title="Secure Payment Form"
      />
    </div>
  );
};

export default PaymentIntegration;`;