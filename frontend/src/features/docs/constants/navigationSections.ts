export const IFRAME_DOC_SECTIONS = [
  {
    id: "overview",
    title: "Overview",
    subsections: [
      { id: "what-is-iframe", title: "What is Iframe Integration?" },
      { id: "benefits", title: "Benefits & Features" },
      { id: "security", title: "Security Features" },
    ],
  },
  {
    id: "quickstart",
    title: "Quick Start",
    subsections: [
      { id: "step-1", title: "Step 1: Generate Token" },
      { id: "step-2", title: "Step 2: Embed Iframe" },
      { id: "step-3", title: "Step 3: Handle Events" },
    ],
  },
  {
    id: "examples",
    title: "Code Examples",
    subsections: [
      { id: "html-example", title: "HTML Example" },
      { id: "react-example", title: "React Example" },
      { id: "javascript-example", title: "JavaScript Example" },
    ],
  },
  {
    id: "api",
    title: "API Reference",
    subsections: [
      { id: "token-generation", title: "Token Generation" },
      { id: "iframe-events", title: "Iframe Events" },
      { id: "error-handling", title: "Error Handling" },
    ],
  },
];

export const BENEFITS_LIST = [
  "Seamless user experience - no redirects",
  "PCI compliant - card data never touches your servers",
  "Customizable styling to match your brand",
  "Real-time payment status updates",
  "Mobile responsive design",
  "Secure token-based authentication",
];

export const HOW_IT_WORKS_STEPS = [
  "Generate a secure payment token via API",
  "Embed iframe with the token in your page",
  "Customer enters payment details securely",
  "Receive real-time payment status updates",
  "Handle success/failure in your application",
];

export const TOKEN_SECURITY_FEATURES = [
  "Cryptographically secure tokens",
  "Configurable expiration times",
  "Single-use token validation",
];

export const PCI_COMPLIANCE_FEATURES = [
  "Card data processed in secure iframes",
  "No sensitive data on your servers",
  "Payrix handles PCI compliance",
];