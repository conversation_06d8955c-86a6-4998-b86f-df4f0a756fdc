export const IFRAME_EVENTS = {
  ready: {
    name: "PAYMENT_IFRAME_READY",
    description: "Fired when the iframe has loaded and is ready to accept payments.",
    example: `{
  type: 'PAYMENT_IFRAME_READY',
  data: {
    merchantName: 'Merchant Name',
    amount: 2500,
    description: 'Product Purchase'
  }
}`
  },
  
  scriptLoaded: {
    name: "PAYFIELDS_SCRIPT_LOADED",
    description: "Fired when the Payrix PayFields script has loaded successfully.",
    example: `{
  type: 'PAYFIELDS_SCRIPT_LOADED',
  timestamp: '2024-01-01T12:00:00.000Z'
}`
  },
  
  submissionStarted: {
    name: "PAYMENT_SUBMISSION_STARTED",
    description: "Fired when payment submission begins.",
    example: `{
  type: 'PAYMENT_SUBMISSION_STARTED',
  timestamp: '2024-01-01T12:00:00.000Z'
}`
  },
  
  success: {
    name: "PAYMENT_SUCCESS",
    description: "Fired when a payment is successfully processed.",
    example: `{
  type: 'PAYMENT_SUCCESS',
  data: {
    // Payment response data from Payrix
    transactionId: 'txn_123456',
    amount: 2500,
    status: 'approved'
  },
  timestamp: '2024-01-01T12:00:00.000Z'
}`
  },
  
  failure: {
    name: "PAYMENT_FAILURE",
    description: "Fired when a payment fails.",
    example: `{
  type: 'PAYMENT_FAILURE',
  error: 'Payment declined',
  details: { /* Error details */ },
  timestamp: '2024-01-01T12:00:00.000Z'
}`
  },
  
  validationFailure: {
    name: "PAYMENT_VALIDATION_FAILURE",
    description: "Fired when payment validation fails (e.g., invalid card details).",
    example: `{
  type: 'PAYMENT_VALIDATION_FAILURE',
  error: 'Invalid card number',
  details: { /* Validation error details */ },
  timestamp: '2024-01-01T12:00:00.000Z'
}`
  },
  
  redirect: {
    name: "PAYMENT_REDIRECT",
    description: "Fired when the payment is successful and a return URL was provided.",
    example: `{
  type: 'PAYMENT_REDIRECT',
  url: 'https://yoursite.com/success'
}`
  },
  
  timeout: {
    name: "PAYMENT_TIMEOUT",
    description: "Fired when payment processing times out.",
    example: `{
  type: 'PAYMENT_TIMEOUT',
  error: 'Payment processing timed out',
  timestamp: '2024-01-01T12:00:00.000Z'
}`
  }
};

export const ERROR_HANDLING = {
  description: "All API endpoints return consistent error responses:",
  format: `{
  "success": false,
  "error": "Error type",
  "message": "Human-readable error message",
  "details": "Additional error details (optional)"
}`,
  statusCodes: [
    "200: Success",
    "400: Bad Request (validation errors)",
    "401: Unauthorized (invalid token)",
    "404: Not Found (merchant not found)",
    "413: Payload Too Large",
    "500: Internal Server Error"
  ]
};

export const SECURITY_CONSIDERATIONS = [
  "Token Security: Tokens are cryptographically secure and single-use",
  "CORS: Configure allowed origins for production",
  "HTTPS: Always use HTTPS in production",
  "CSP: Implement Content Security Policy headers",
  "Input Validation: All inputs are validated server-side",
  "Rate Limiting: API endpoints include basic rate limiting"
];