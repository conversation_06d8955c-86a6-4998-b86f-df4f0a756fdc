export const HTML_EXAMPLE = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Integration Example</title>
    <style>
        .payment-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #fff;
        }
        .payment-iframe {
            width: 100%;
            height: 600px;
            border: none;
            border-radius: 8px;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #64748b;
        }
        .error {
            text-align: center;
            padding: 40px;
            color: #dc2626;
            background-color: #fee2e2;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <h2>Secure Payment</h2>
        <div id="payment-iframe-container">
            <div class="loading">Loading payment form...</div>
        </div>
    </div>

    <script>
        // Your merchant configuration
        const merchantConfig = {
            merchantId: 'your-merchant-id',
            description: 'Product Purchase',
            amount: 2500, // $25.00 in cents
            returnUrl: 'https://yoursite.com/payment-success'
        };

        // Generate payment token and load iframe
        async function loadPaymentIframe() {
            try {
                // Step 1: Generate integration token
                const response = await fetch('https://your-api-domain.com/payments/generate-integration-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(merchantConfig)
                });

                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.message || 'Failed to generate payment token');
                }

                // Step 2: Create and load iframe
                const iframe = document.createElement('iframe');
                iframe.src = result.data.embedUrl;
                iframe.className = 'payment-iframe';
                iframe.allow = 'payment';
                
                // Replace loading message with iframe
                const container = document.getElementById('payment-iframe-container');
                while (container.firstChild) {
                    container.removeChild(container.firstChild);
                }
                container.appendChild(iframe);

                // Step 3: Listen for payment events
                window.addEventListener('message', handlePaymentMessage);

            } catch (error) {
                console.error('Payment integration error:', error);
                const container = document.getElementById('payment-iframe-container');
                while (container.firstChild) {
                    container.removeChild(container.firstChild);
                }
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error';
                errorDiv.textContent = 'Failed to load payment form. Please try again.';
                container.appendChild(errorDiv);
            }
        }

        // Handle messages from payment iframe
        function handlePaymentMessage(event) {
            // Verify origin for security (replace with your actual domain)
            if (event.origin !== 'https://your-payment-domain.com') {
                return;
            }

            switch (event.data.type) {
                case 'PAYMENT_IFRAME_READY':
                    console.log('Payment iframe loaded successfully');
                    break;
                    
                case 'PAYMENT_SUCCESS':
                    console.log('Payment successful:', event.data.data);
                    // Handle successful payment
                    alert('Payment successful! Thank you for your purchase.');
                    break;
                    
                case 'PAYMENT_FAILURE':
                    console.error('Payment failed:', event.data.error);
                    // Handle payment failure
                    alert('Payment failed: ' + event.data.error);
                    break;
                    
                case 'PAYMENT_REDIRECT':
                    // Handle redirect after successful payment
                    window.location.href = event.data.url;
                    break;
                    
                case 'PAYMENT_IFRAME_ERROR':
                    console.error('Iframe error:', event.data.error);
                    // Handle iframe loading errors
                    break;
            }
        }

        // Load payment iframe when page loads
        document.addEventListener('DOMContentLoaded', loadPaymentIframe);
    </script>
</body>
</html>`;