import { API_ENDPOINTS } from "./apiEndpoints";
import { IFRAME_EVENTS, ERROR_HANDLING, SECURITY_CONSIDERATIONS } from "./iframeEvents";

const formatEndpoint = (endpoint: (typeof API_ENDPOINTS)[keyof typeof API_ENDPOINTS]) => {
  let section = `## ${endpoint.title}\n\n`;
  section += `**Endpoint:** \`${endpoint.endpoint}\`\n\n`;
  section += `**Description:** ${endpoint.description}\n\n`;

  if ("requestBody" in endpoint && endpoint.requestBody) {
    section += `**Request Body:**\n\`\`\`json\n${endpoint.requestBody}\n\`\`\`\n\n`;
  }

  if ("queryParams" in endpoint && endpoint.queryParams && Array.isArray(endpoint.queryParams)) {
    section += `**Query Parameters:**\n`;
    endpoint.queryParams.forEach((param: string) => {
      section += `- \`${param}\`\n`;
    });
    section += "\n";
  }

  section += `**Response:**\n\`\`\`json\n${endpoint.response}\n\`\`\`\n\n`;
  return section;
};

const formatIframeEvent = (event: (typeof IFRAME_EVENTS)[keyof typeof IFRAME_EVENTS]) => {
  let section = `### ${event.name}\n`;
  section += `${event.description}\n`;
  section += `\`\`\`javascript\n${event.example}\n\`\`\`\n\n`;
  return section;
};

export const API_DOCUMENTATION = `
# API Documentation

${formatEndpoint(API_ENDPOINTS.generateToken)}
${formatEndpoint(API_ENDPOINTS.validateToken)}
${formatEndpoint(API_ENDPOINTS.tokenStatus)}

## Iframe Events

The payment iframe communicates with the parent window using \`postMessage\`. Listen for these events:

${formatIframeEvent(IFRAME_EVENTS.ready)}
${formatIframeEvent(IFRAME_EVENTS.scriptLoaded)}
${formatIframeEvent(IFRAME_EVENTS.submissionStarted)}
${formatIframeEvent(IFRAME_EVENTS.success)}
${formatIframeEvent(IFRAME_EVENTS.failure)}
${formatIframeEvent(IFRAME_EVENTS.validationFailure)}
${formatIframeEvent(IFRAME_EVENTS.redirect)}
${formatIframeEvent(IFRAME_EVENTS.timeout)}

## Error Handling

${ERROR_HANDLING.description}

\`\`\`json
${ERROR_HANDLING.format}
\`\`\`

Common HTTP status codes:
${ERROR_HANDLING.statusCodes.map((code) => `- \`${code}\``).join("\n")}

## Security Considerations

${SECURITY_CONSIDERATIONS.map((item, idx) => `${idx + 1}. **${item}**`).join("\n")}
`;
