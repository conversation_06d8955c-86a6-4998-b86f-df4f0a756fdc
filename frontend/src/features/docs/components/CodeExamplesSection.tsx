import { ContentCard } from "../../../components/ContentCard";
import { CodeBlock } from "../../../components/common/CodeBlock";
import { HTML_EXAMPLE, REACT_EXAMPLE } from "../constants/codeExamples";

export const CodeExamplesSection = () => {
  return (
    <>
      <ContentCard id="html-example" title="Complete HTML Example">
        <CodeBlock code={HTML_EXAMPLE} label="A complete HTML page with iframe integration" />
      </ContentCard>

      <ContentCard id="react-example" title="React/JavaScript Example">
        <CodeBlock code={REACT_EXAMPLE} label="React component with iframe integration" />
      </ContentCard>
    </>
  );
};