import { ContentCard } from "../../../components/ContentCard";
import { CURL_EXAMPLE, IFRAME_EMBED_EXAMPLE, EVENT_LISTENER_EXAMPLE } from "../constants/codeExamples";

export const QuickStartSection = () => {
  return (
    <>
      <ContentCard id="step-1" title="Step 1: Generate Payment Token" variant="highlight">
        <p className="text-slate-600 mb-4">Make a POST request to generate a secure payment token:</p>
        <div className="bg-slate-900 text-slate-300 p-6 rounded-lg font-mono text-sm overflow-x-auto">
          <pre className="text-slate-200">{CURL_EXAMPLE}</pre>
        </div>
      </ContentCard>

      <ContentCard id="step-2" title="Step 2: Embed Iframe" variant="success">
        <p className="text-slate-600 mb-4">Use the returned embedUrl to create an iframe:</p>
        <div className="bg-slate-900 text-slate-300 p-6 rounded-lg font-mono text-sm overflow-x-auto">
          <pre className="text-slate-200">{IFRAME_EMBED_EXAMPLE}</pre>
        </div>
      </ContentCard>

      <ContentCard id="step-3" title="Step 3: Handle Events">
        <p className="text-slate-600 mb-4">Listen for payment events from the iframe:</p>
        <div className="bg-slate-900 text-slate-300 p-6 rounded-lg font-mono text-sm overflow-x-auto">
          <pre className="text-slate-200">{EVENT_LISTENER_EXAMPLE}</pre>
        </div>
      </ContentCard>
    </>
  );
};