{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "deploy": "npm run build && npm run upload && npm run update-cloudfront-id && npm run invalidate", "update-cloudfront-id": "./update-cloudfront-id.sh dev payrix", "upload": "aws s3 sync dist/ s3://auth-clear-frontend-dev --profile payrix --delete", "invalidate": "aws cloudfront create-invalidation --distribution-id E155BFWC2XG8CI --paths '/*' --profile payrix", "deploy:dev": "npm run deploy", "deploy:prod": "npm run build && aws s3 sync dist/ s3://auth-clear-frontend-prod --profile payrix --delete && aws cloudfront create-invalidation --distribution-id E155BFWC2XG8CI --paths '/*' --profile payrix"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.8", "@types/react-plaid-link": "^1.3.1", "axios": "^1.9.0", "lucide-react": "^0.542.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^6.0.0", "react-plaid-link": "^4.1.1", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "redux-persist": "^6.0.0", "sonner": "^2.0.5", "tailwindcss": "^4.1.8"}, "devDependencies": {"@eslint/js": "^9.28.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.28.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.2.0", "typescript": "~5.8.3", "typescript-eslint": "^8.33.1", "vite": "^6.3.5"}}