import { CompliancePolicies } from "./integration-token.types.js";

export interface TokenData {
  merchantId: string;
  description: string;
  amount: number;
  returnUrl?: string;
  expiresAt: Date;
  used: boolean;
  currency?: string;
  items?: Array<{
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    total: number;
    commodityCode?: string;
    productCode?: string;
  }>;
  taxAmount?: number;
  shippingAmount?: number;
  dutyAmount?: number;
  orderNumber?: string;
  invoiceNumber?: string;
  customerCode?: string;
  orderDiscount?: number;
  compliancePolicies?: CompliancePolicies;
  merchantInfo?: {
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
    contactEmail?: string;
    contactPhone?: string;
  };
}

export interface StoredTokenData {
  merchantId: string;
  description: string;
  amount: number;
  returnUrl?: string;
  expiresAt: string;
  used: boolean;
  currency?: string;
  items?: Array<{
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    total: number;
    commodityCode?: string;
    productCode?: string;
  }>;
  taxAmount?: number;
  shippingAmount?: number;
  dutyAmount?: number;
  orderNumber?: string;
  invoiceNumber?: string;
  customerCode?: string;
  orderDiscount?: number;
  compliancePolicies?: CompliancePolicies;
  merchantInfo?: {
    address?: {
      line1?: string;
      line2?: string;
      city?: string;
      state?: string;
      zip?: string;
      country?: string;
    };
    contactEmail?: string;
    contactPhone?: string;
  };
}

export interface DynamoTokenItem extends Omit<TokenData, "expiresAt"> {
  tokenId: string;
  expiresAt: number;
}

export interface TokenValidationResult {
  isValid: boolean;
  data?: TokenData;
  error?: string;
}

export interface TokenDataValidationResult {
  isValid: boolean;
  errors: string[];
}
