import { z } from "zod";
import { validateToken } from "./generate-integration-token.js";
import { validateTokenFormat } from "../../middleware/security.js";
import { create<PERSON>frame<PERSON><PERSON><PERSON>, BusinessError, HandlerContext } from "../../middleware/base-handler.js";
import { HTTP_STATUS } from "../../constants/http.constants.js";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "../../constants/messages.constants.js";
import { TOKEN_STATUS, TIME_CONVERSION } from "../../constants/validation.constants.js";

interface TokenStatusRequest {
  token?: string;
}

interface TokenStatusResponse {
  isValid: boolean;
  status: typeof TOKEN_STATUS[keyof typeof TOKEN_STATUS];
  expiresAt?: string;
  timeRemaining?: number;
  merchantId?: string;
  amount?: number;
  description?: string;
}

const tokenStatusSchema = z.object({
  token: z.string().min(1, "Token is required").optional(),
});

const processTokenStatus = async ({ body, event }: HandlerContext<TokenStatusRequest>) => {
  let token: string;
  
  if (event.httpMethod === "GET") {
    token = event.queryStringParameters?.token || "";
  } else {
    if (!body?.token) {
      throw new BusinessError("Please provide a token", HTTP_STATUS.BAD_REQUEST, ERROR_MESSAGES.BODY_REQUIRED);
    }
    token = body.token;
  }

  const tokenFormatValidation = validateTokenFormat(token);
  if (!tokenFormatValidation.isValid) {
    throw new BusinessError(
      ERROR_MESSAGES.INVALID_TOKEN_FORMAT,
      tokenFormatValidation.statusCode || HTTP_STATUS.BAD_REQUEST,
      tokenFormatValidation.error
    );
  }

  const tokenValidation = await validateToken(token);
  let response: TokenStatusResponse;

  if (tokenValidation.isValid && tokenValidation.data) {
    const now = new Date();
    const expiresAt = new Date(now.getTime() + TIME_CONVERSION.HOURS_TO_MS);
    const timeRemaining = Math.max(0, Math.floor((expiresAt.getTime() - now.getTime()) / TIME_CONVERSION.SECONDS_TO_MS));

    response = {
      isValid: true,
      status: TOKEN_STATUS.VALID,
      expiresAt: expiresAt.toISOString(),
      timeRemaining,
      merchantId: tokenValidation.data.merchantId,
      amount: tokenValidation.data.amount,
      description: tokenValidation.data.description,
    };
  } else {
    let status: typeof TOKEN_STATUS[keyof typeof TOKEN_STATUS] = TOKEN_STATUS.INVALID;

    if (tokenValidation.error?.includes("expired")) {
      status = TOKEN_STATUS.EXPIRED;
    } else if (tokenValidation.error?.includes("used")) {
      status = TOKEN_STATUS.USED;
    }

    response = {
      isValid: false,
      status,
    };
  }

  return response;
};

export const handler = createIframeHandler(processTokenStatus, {
  schema: tokenStatusSchema,
  successMessage: SUCCESS_MESSAGES.TOKEN_STATUS_RETRIEVED,
  errorMessage: ERROR_MESSAGES.PROCESSING_FAILED,
});