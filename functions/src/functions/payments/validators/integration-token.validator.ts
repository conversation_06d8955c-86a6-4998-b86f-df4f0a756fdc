import { IntegrationTokenRequest } from "../../../types/integration-token.types.js";
import {
  validateMerchantIdFormat,
  validateDescription,
  validateAmount,
  validateReturnUrl,
} from "../../../middleware/security.js";
import { ERROR_MESSAGES } from "../../../constants/messages.constants.js";
import { VALIDATION_LIMITS, DEFAULTS } from "../../../constants/validation.constants.js";
import { HTTP_STATUS } from "../../../constants/http.constants.js";

export interface ValidationResult {
  isValid: boolean;
  statusCode?: number;
  error?: string;
  message?: string;
  details?: Record<string, unknown>;
}

export function validateTaxAmount(taxAmount: number | undefined, amount: number): ValidationResult {
  if (taxAmount !== undefined && taxAmount > 0) {
    const taxPercentage = (taxAmount / amount) * 100;
    if (taxPercentage < VALIDATION_LIMITS.MIN_TAX_PERCENTAGE || taxPercentage > VALIDATION_LIMITS.MAX_TAX_PERCENTAGE) {
      return {
        isValid: false,
        statusCode: HTTP_STATUS.BAD_REQUEST,
        error: "Invalid tax amount",
        message: ERROR_MESSAGES.INVALID_TAX_AMOUNT,
        details: {
          taxAmount,
          amount,
          percentage: taxPercentage.toFixed(2),
        },
      };
    }
  }
  return { isValid: true };
}

export function validateItemizedTransaction(request: IntegrationTokenRequest): ValidationResult {
  if (request.items && request.items.length > 0) {
    if (!request.orderNumber && !request.invoiceNumber && !request.customerCode) {
      return {
        isValid: false,
        statusCode: HTTP_STATUS.BAD_REQUEST,
        error: ERROR_MESSAGES.MISSING_REQUIRED_FIELD,
        message: ERROR_MESSAGES.ITEMIZED_TRANSACTION_REQUIREMENTS,
      };
    }
  }
  return { isValid: true };
}

export function validateIntegrationTokenRequest(request: IntegrationTokenRequest): ValidationResult[] {
  const validations: ValidationResult[] = [];

  const merchantValidation = validateMerchantIdFormat(request.merchantId);
  if (!merchantValidation.isValid) {
    validations.push({
      isValid: false,
      statusCode: merchantValidation.statusCode || HTTP_STATUS.BAD_REQUEST,
      error: merchantValidation.error,
      message: "Invalid merchant ID",
    });
  }

  const descriptionValidation = validateDescription(request.description);
  if (!descriptionValidation.isValid) {
    validations.push({
      isValid: false,
      statusCode: descriptionValidation.statusCode || HTTP_STATUS.BAD_REQUEST,
      error: descriptionValidation.error,
      message: ERROR_MESSAGES.INVALID_DESCRIPTION,
    });
  }

  const amountValidation = validateAmount(request.amount || DEFAULTS.AMOUNT);
  if (!amountValidation.isValid) {
    validations.push({
      isValid: false,
      statusCode: amountValidation.statusCode || HTTP_STATUS.BAD_REQUEST,
      error: amountValidation.error,
      message: ERROR_MESSAGES.INVALID_AMOUNT,
    });
  }

  if (request.returnUrl) {
    const urlValidation = validateReturnUrl(request.returnUrl);
    if (!urlValidation.isValid) {
      validations.push({
        isValid: false,
        statusCode: urlValidation.statusCode || HTTP_STATUS.BAD_REQUEST,
        error: urlValidation.error,
        message: ERROR_MESSAGES.INVALID_RETURN_URL,
      });
    }
  }

  const taxValidation = validateTaxAmount(request.taxAmount, request.amount || DEFAULTS.AMOUNT);
  if (!taxValidation.isValid) {
    validations.push(taxValidation);
  }

  const itemValidation = validateItemizedTransaction(request);
  if (!itemValidation.isValid) {
    validations.push(itemValidation);
  }

  return validations;
}