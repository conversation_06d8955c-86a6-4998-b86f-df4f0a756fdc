import { z } from "zod";

const itemSchema = z.object({
  name: z.string().min(1, "Item name is required"),
  description: z.string().optional(),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
  unitPrice: z.number().int().min(0, "Unit price must be non-negative"),
  total: z.number().int().min(0, "Total must be non-negative"),
  commodityCode: z.string().optional(),
  productCode: z.string().optional(),
});

const googlePayConfigSchema = z
  .object({
    enabled: z.boolean().optional(),
    merchantName: z.string().optional(),
    environment: z.enum(["TEST", "PRODUCTION"]).optional(),
    allowedCardNetworks: z.array(z.enum(["VISA", "MASTERCARD", "AMEX", "DISCOVER", "JCB", "INTERAC"])).optional(),
    allowedCardAuthMethods: z.array(z.enum(["PAN_ONLY", "CRYPTOGRAM_3DS"])).optional(),
    billingAddressRequired: z.boolean().optional(),
    shippingAddressRequired: z.boolean().optional(),
    phoneNumberRequired: z.boolean().optional(),
  })
  .optional();

// merchantPolicySchema and compliancePoliciesSchema removed - policies now retrieved from DynamoDB during token generation

export const tokenRequestSchema = z.object({
  merchantId: z.string().min(1, "Merchant ID is required"),
  description: z.string().min(1, "Description is required"),
  amount: z.number().int().min(1, "Amount must be at least 1 cent").optional(),
  returnUrl: z.string().url("Return URL must be a valid URL").optional(),
  expiresIn: z.number().int().min(1).max(1440, "Expires in must be between 1 and 1440 minutes").optional().default(60),
  currency: z.string().length(3, "Currency must be 3-letter ISO code").optional().default("USD"),
  items: z.array(itemSchema).optional(),
  taxAmount: z.number().int().min(0, "Tax amount must be non-negative").optional(),
  shippingAmount: z.number().int().min(0, "Shipping amount must be non-negative").optional(),
  dutyAmount: z.number().int().min(0, "Duty amount must be non-negative").optional(),
  orderNumber: z.string().optional(),
  invoiceNumber: z.string().optional(),
  customerCode: z.string().optional(),
  orderDiscount: z.number().int().min(0, "Discount must be non-negative").optional(),
  googlePayConfig: googlePayConfigSchema,
  enableDigitalWallets: z.boolean().optional(),
  // compliancePolicies removed - now retrieved from DynamoDB
});
