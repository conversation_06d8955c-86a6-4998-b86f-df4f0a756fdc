import { z } from "zod";
import { validateMerchantById } from "../../service/payrix.service.js";
import { validateToken } from "./generate-integration-token";
import { validateTokenFormat } from "../../middleware/security.js";
import { create<PERSON><PERSON><PERSON><PERSON>ler, BusinessError, HandlerContext } from "../../middleware/base-handler.js";
import { HTTP_STATUS } from "../../constants/http.constants.js";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "../../constants/messages.constants.js";
import { DEFAULTS, PAYMENT_METHODS } from "../../constants/validation.constants.js";

interface TokenValidationRequest {
  token: string;
  paymentMethod?: "card" | "google_pay";
}

const tokenValidationSchema = z.object({
  token: z.string().min(1, "Token is required"),
  paymentMethod: z.enum(["card", "google_pay"]).optional(),
});

const processValidateToken = async ({ body }: HandlerContext<TokenValidationRequest>) => {
  if (!body) {
    throw new BusinessError("Please provide a token", HTTP_STATUS.BAD_REQUEST, ERROR_MESSAGES.BODY_REQUIRED);
  }

  const { token, paymentMethod = PAYMENT_METHODS.CARD } = body;
  
  const tokenFormatValidation = validateTokenFormat(token);
  if (!tokenFormatValidation.isValid) {
    throw new BusinessError(
      ERROR_MESSAGES.INVALID_TOKEN_FORMAT,
      tokenFormatValidation.statusCode || HTTP_STATUS.BAD_REQUEST,
      tokenFormatValidation.error
    );
  }

  const tokenValidation = await validateToken(token);
  if (!tokenValidation.isValid || !tokenValidation.data) {
    throw new BusinessError(
      tokenValidation.error || ERROR_MESSAGES.INVALID_OR_EXPIRED_TOKEN,
      HTTP_STATUS.UNAUTHORIZED,
      ERROR_MESSAGES.TOKEN_VALIDATION_FAILED
    );
  }

  const {
    merchantId,
    description,
    amount,
    returnUrl,
    currency,
    items,
    taxAmount,
    shippingAmount,
    dutyAmount,
    orderNumber,
    invoiceNumber,
    customerCode,
    orderDiscount,
    googlePayConfig,
    enableDigitalWallets,
    compliancePolicies,
  } = tokenValidation.data;

  const merchantValidation = await validateMerchantById(merchantId);
  if (!merchantValidation.isValid) {
    throw new BusinessError(
      merchantValidation.error || ERROR_MESSAGES.INVALID_OR_INACTIVE_MERCHANT,
      HTTP_STATUS.NOT_FOUND,
      ERROR_MESSAGES.MERCHANT_VALIDATION_FAILED,
      {
        merchantId,
        validationError: merchantValidation.error,
      }
    );
  }

  const configAmount = amount || 0;
  const isGooglePay = paymentMethod === PAYMENT_METHODS.GOOGLE_PAY;

  const config = {
    merchantId,
    publicKey: process.env.PAYRIX_PUBLIC_API_KEY || DEFAULTS.PUBLIC_KEY,
    amount: configAmount,
    description,
    mode: DEFAULTS.MODE,
    txnType: DEFAULTS.TXN_TYPE,
    googlePayConfig: isGooglePay ? googlePayConfig : undefined,
    enableDigitalWallets: isGooglePay ? enableDigitalWallets : false,
  };

  const merchant = merchantValidation.merchant as Record<string, unknown> | undefined;

  const merchantInfo = {
    name: (merchant?.dba as string) || (merchant?.legal_name as string) || (merchant?.name as string) || DEFAULTS.MERCHANT_NAME,
    dba: merchant?.dba as string,
    address:
      merchant && (merchant.address1 || merchant.city)
        ? {
            line1: merchant.address1 as string,
            line2: merchant.address2 as string,
            city: merchant.city as string,
            state: merchant.state as string,
            zip: merchant.zip as string,
          }
        : undefined,
    contactEmail: merchant?.email as string,
    contactPhone: merchant?.phone as string,
  };

  return {
    config,
    merchantInfo,
    paymentInfo: {
      description,
      amount,
      currency,
      returnUrl,
      items,
      taxAmount,
      shippingAmount,
      dutyAmount,
      orderNumber,
      invoiceNumber,
      customerCode,
      orderDiscount,
      compliancePolicies,
    },
  };
};

export const handler = createIframeHandler(processValidateToken, {
  schema: tokenValidationSchema,
  successMessage: SUCCESS_MESSAGES.TOKEN_VALIDATED,
  errorMessage: ERROR_MESSAGES.PROCESSING_FAILED,
});