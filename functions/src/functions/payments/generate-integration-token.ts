import { sanitizeInput } from "../../middleware/security.js";
import { validateMerchantById } from "../../service/payrix.service.js";
import { IntegrationTokenRequest } from "../../types/integration-token.types.js";
import { tokenRequestSchema } from "./schemas/integration-token.schema.js";
import { getMerchantPolicies, validateMerchantPolicies } from "../../service/merchant-data.js";
import {
  generateSecureToken,
  storeToken,
  validateToken as validateTokenService,
  markTokenAsUsed as markTokenAsUsedService,
} from "./services/integration-token.service.js";
import { validateIntegrationTokenRequest } from "./validators/integration-token.validator.js";
import { buildTokenData, buildTokenResponse } from "./utils/token-config-builder.js";
import { HTTP_STATUS } from "../../constants/http.constants.js";
import { ERROR_MESSAGES, SUCCESS_MESSAGES, DETAIL_MESSAGES } from "../../constants/messages.constants.js";
import { DEFAULTS, TIME_CONVERSION } from "../../constants/validation.constants.js";
import { createIframeHandler, BusinessError, HandlerContext } from "../../middleware/base-handler.js";

interface MerchantAddress {
  line1?: string;
  line2?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
}

interface PayrixMerchant {
  dba?: string;
  name?: string;
  status?: number;
  address?: MerchantAddress;
  email?: string;
  phone?: string;
}

export const validateToken = validateTokenService;
export const markTokenAsUsed = markTokenAsUsedService;

const processGenerateToken = async ({ body }: HandlerContext<IntegrationTokenRequest>) => {
  if (!body) {
    throw new BusinessError(DETAIL_MESSAGES.PROVIDE_MERCHANT_ID, HTTP_STATUS.BAD_REQUEST, ERROR_MESSAGES.BODY_REQUIRED);
  }

  const requestData = body;

  const validationErrors = validateIntegrationTokenRequest(requestData);
  if (validationErrors.length > 0) {
    const firstError = validationErrors[0];
    throw new BusinessError(
      firstError.message || ERROR_MESSAGES.INVALID_REQUEST,
      firstError.statusCode || HTTP_STATUS.BAD_REQUEST,
      firstError.error,
      firstError.details
    );
  }

  const {
    merchantId,
    description,
    amount = DEFAULTS.AMOUNT,
    returnUrl,
    expiresIn = DEFAULTS.EXPIRES_IN_MINUTES,
    currency = DEFAULTS.CURRENCY,
    items,
    taxAmount,
    shippingAmount,
    dutyAmount,
    orderNumber,
    invoiceNumber,
    customerCode,
    orderDiscount,
    googlePayConfig,
    enableDigitalWallets,
  } = requestData;

  const sanitizedDescription = sanitizeInput(description);

  const validation = await validateMerchantById(merchantId);
  if (!validation.isValid) {
    throw new BusinessError(
      validation.error || ERROR_MESSAGES.INVALID_OR_INACTIVE_MERCHANT,
      HTTP_STATUS.NOT_FOUND,
      ERROR_MESSAGES.MERCHANT_VALIDATION_FAILED,
      { merchantId, validationError: validation.error }
    );
  }

  const merchant = validation.merchant as PayrixMerchant;

  const compliancePolicies = await getMerchantPolicies(merchantId);
  if (!compliancePolicies) {
    throw new BusinessError(
      ERROR_MESSAGES.MISSING_COMPLIANCE_POLICIES,
      HTTP_STATUS.BAD_REQUEST,
      "MISSING_COMPLIANCE_POLICIES",
      DETAIL_MESSAGES.COMPLIANCE_REQUIRED
    );
  }

  const policyValidation = await validateMerchantPolicies(merchantId);
  if (!policyValidation.isValid) {
    throw new BusinessError(ERROR_MESSAGES.INVALID_COMPLIANCE_POLICIES, HTTP_STATUS.BAD_REQUEST, "INVALID_COMPLIANCE_POLICIES", {
      missingPolicies: policyValidation.missingPolicies,
      errors: policyValidation.errors,
    });
  }

  const token = generateSecureToken();
  const expiresAt = new Date(Date.now() + expiresIn * TIME_CONVERSION.MINUTES_TO_MS);

  const tokenData = buildTokenData(
    merchantId,
    sanitizedDescription,
    amount,
    expiresAt,
    returnUrl,
    {
      currency,
      items,
      taxAmount,
      shippingAmount,
      dutyAmount,
      orderNumber,
      invoiceNumber,
      customerCode,
      orderDiscount,
    },
    merchant?.address || merchant?.email || merchant?.phone
      ? {
          address: merchant?.address
            ? {
                line1: merchant.address.line1,
                line2: merchant.address.line2,
                city: merchant.address.city,
                state: merchant.address.state,
                zip: merchant.address.zip,
                country: merchant.address.country || DEFAULTS.COUNTRY,
              }
            : undefined,
          email: merchant?.email,
          phone: merchant?.phone,
        }
      : undefined,
    {
      googlePayConfig,
      enableDigitalWallets,
    },
    compliancePolicies
  );

  await storeToken(token, tokenData);

  return buildTokenResponse(token, expiresAt, {
    id: merchantId,
    name: merchant?.dba || merchant?.name,
    status: merchant?.status,
  });
};

const processCleanupToken = async ({ body }: HandlerContext<{ token: string }>) => {
  if (!body?.token) {
    throw new BusinessError("Token is required", HTTP_STATUS.BAD_REQUEST, ERROR_MESSAGES.BODY_REQUIRED);
  }

  const { token } = body;

  // Validate token format
  if (!token || typeof token !== "string" || token.length < 10) {
    throw new BusinessError("Invalid token format", HTTP_STATUS.BAD_REQUEST, ERROR_MESSAGES.INVALID_REQUEST);
  }

  try {
    // Mark token as used in storage
    const marked = await markTokenAsUsed(token);

    if (!marked) {
      throw new BusinessError("Token not found or already used", HTTP_STATUS.NOT_FOUND, "TOKEN_NOT_FOUND_OR_USED");
    }

    return {
      success: true,
      message: "Token marked as used and cleaned up successfully",
      data: {
        used: true,
        deleted: true,
      },
    };
  } catch (error) {
    if (error instanceof BusinessError) {
      throw error;
    }

    throw new BusinessError("Failed to cleanup token", HTTP_STATUS.INTERNAL_ERROR, "TOKEN_CLEANUP_FAILED", {
      originalError: error instanceof Error ? error.message : "Unknown error",
    });
  }
};

export const handler = createIframeHandler(processGenerateToken, {
  schema: tokenRequestSchema,
  successMessage: SUCCESS_MESSAGES.TOKEN_GENERATED,
  errorMessage: ERROR_MESSAGES.TOKEN_GENERATION_FAILED,
});

export const cleanupHandler = createIframeHandler(processCleanupToken, {
  successMessage: "Token cleanup completed",
  errorMessage: "Token cleanup failed",
});
