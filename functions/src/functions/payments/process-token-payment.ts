import { z } from "zod";
import { logger } from "../../helpers/logger.js";
import { processPayment } from "./services/token-payment-processor.service.js";
import { create<PERSON><PERSON><PERSON>, BusinessError, HandlerContext } from "../../middleware/base-handler.js";
import { HTTP_STATUS } from "../../constants/http.constants.js";
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from "../../constants/messages.constants.js";
import { validateMerchantIdFormat, validateAmount, validateTokenFormat, sanitizeInput } from "../../middleware/security.js";

const TOKEN_DISPLAY = {
  PREFIX_LENGTH: 8,
  SUFFIX: "...",
} as const;

const tokenPaymentSchema = z.object({
  merchantId: z.string().min(1, "Merchant ID is required"),
  token: z.string().min(1, "Token is required"),
  tokenId: z.string().optional(),
  amount: z.number().min(0, "Amount must be non-negative"),
  description: z.string().optional(),
  paymentType: z.enum(["CARD", "GOOGLE_PAY", "APPLE_PAY"]).optional(),
  customerInfo: z
    .object({
      email: z.string().email().optional(),
      name: z.string().optional(),
      address: z
        .object({
          line1: z.string().optional(),
          line2: z.string().optional(),
          city: z.string().optional(),
          state: z.string().optional(),
          zip: z.string().optional(),
          country: z.string().optional(),
        })
        .optional(),
    })
    .optional(),
});

type TokenPaymentRequest = z.infer<typeof tokenPaymentSchema>;

const processTokenPayment = async ({ body, event }: HandlerContext<TokenPaymentRequest>) => {
  const requestId = event.requestContext.requestId;

  if (!body) {
    throw new BusinessError(
      "Request body is required",
      HTTP_STATUS.BAD_REQUEST,
      ERROR_MESSAGES.BODY_REQUIRED
    );
  }

  // Validate merchantId format
  const merchantIdValidation = validateMerchantIdFormat(body.merchantId);
  if (!merchantIdValidation.isValid) {
    throw new BusinessError(
      merchantIdValidation.error || "Invalid merchant ID format",
      merchantIdValidation.statusCode || HTTP_STATUS.BAD_REQUEST,
      ERROR_MESSAGES.VALIDATION_FAILED
    );
  }

  // Validate token format
  const tokenValidation = validateTokenFormat(body.token);
  if (!tokenValidation.isValid) {
    throw new BusinessError(
      tokenValidation.error || "Invalid token format",
      tokenValidation.statusCode || HTTP_STATUS.BAD_REQUEST,
      ERROR_MESSAGES.INVALID_TOKEN_FORMAT
    );
  }

  // Validate amount
  const amountValidation = validateAmount(body.amount);
  if (!amountValidation.isValid) {
    throw new BusinessError(
      amountValidation.error || "Invalid amount",
      amountValidation.statusCode || HTTP_STATUS.BAD_REQUEST,
      ERROR_MESSAGES.INVALID_AMOUNT
    );
  }

  // Sanitize description if provided
  const sanitizedData = {
    ...body,
    description: body.description ? sanitizeInput(body.description) : undefined,
  };

  logger.info("Processing token payment", {
    requestId,
    merchantId: sanitizedData.merchantId,
    token: sanitizedData.token.substring(0, TOKEN_DISPLAY.PREFIX_LENGTH) + TOKEN_DISPLAY.SUFFIX,
    amount: sanitizedData.amount,
    description: sanitizedData.description,
  });

  const processingResult = await processPayment(sanitizedData);

  if (!processingResult.success) {
    const isMerchantError =
      processingResult.error?.includes("Merchant validation failed") ||
      processingResult.error?.includes("Invalid or inactive merchant");

    if (isMerchantError) {
      throw new BusinessError(
        processingResult.error || ERROR_MESSAGES.INVALID_OR_INACTIVE_MERCHANT,
        HTTP_STATUS.NOT_FOUND,
        ERROR_MESSAGES.MERCHANT_VALIDATION_FAILED,
        {
          merchantId: sanitizedData.merchantId,
        }
      );
    }

    throw new BusinessError(
      processingResult.error || "Payment processing failed",
      HTTP_STATUS.BAD_REQUEST,
      "PAYMENT_PROCESSING_FAILED",
      {
        merchantId: sanitizedData.merchantId,
        amount: sanitizedData.amount,
      }
    );
  }

  logger.info("Token payment completed successfully", {
    requestId,
    merchantId: sanitizedData.merchantId,
    transactionId: processingResult.transaction?.id,
    amount: sanitizedData.amount,
  });

  return processingResult;
};

export const handler = createHandler(processTokenPayment, {
  useIframeResponse: true,
  withSecurity: true,
  schema: tokenPaymentSchema,
  successMessage: SUCCESS_MESSAGES.PAYMENT_PROCESSED,
  errorMessage: "Failed to process token payment",
});