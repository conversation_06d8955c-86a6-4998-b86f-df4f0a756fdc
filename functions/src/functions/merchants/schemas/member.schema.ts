import { z } from "zod";
import { emailSchema, dobSchema, phoneSchema, ssnSchema } from "./validation-helpers.schema.js";

export const memberSchema = z.object({
  first: z.string().min(1, "First name is required"),
  last: z.string().min(1, "Last name is required"),
  email: emailSchema,
  dob: dobSchema,
  title: z.string().min(1, "Business title is required"),
  phone: phoneSchema,
  address1: z.string().min(1, "Address is required"),
  address2: z.string().optional(),
  city: z.string().min(1, "City is required"),
  state: z.string().length(2, "State must be 2 characters"),
  zip: z.string().min(5, "ZIP code is required"),
  ssn: ssnSchema,
  // Fields based on working implementation (restored from c2fc280^)
  ownership: z
    .number()
    .int()
    .min(1, "Ownership percentage must be at least 1%")
    .max(10000, "Ownership percentage cannot exceed 100% (10000 basis points)"),
  significantResponsibility: z.number().int().min(0).max(1, "Significant responsibility must be 0 or 1"),
  politicallyExposed: z.number().int().min(0).max(1, "Politically exposed must be 0 or 1"),
  primary: z.string().optional(), // String representation as in working version
  // Optional fields from working version
  dl: z.string().optional(),
  dlstate: z.string().optional(),
  middle: z.string().optional(),
  country: z.string().default("USA"),
});
