import { z } from "zod";
import { getMCCCodeDetails, isApprovedMCCCode } from "../../../constants/approvedMccCodes";

export const phoneSchema = z
  .string()
  .min(10, "Phone number must be at least 10 digits")
  .regex(/^\+?[\d\s\-()]+$/, "Invalid phone number format")
  .transform((val) => val.replace(/\D/g, ""));

export const emailSchema = z.string().email("Invalid email format").min(1, "Email is required");

export const einSchema = z
  .string()
  .min(1, "EIN is required")
  .regex(/^\d{2}-?\d{7}$/, "EIN must be 9 digits (XX-XXXXXXX or XXXXXXXXX format)")
  .transform((val) => val.replace(/\D/g, ""));

export const zipSchema = z
  .string()
  .min(5, "Zip code must be at least 5 digits")
  .max(10, "Zip code too long")
  .regex(/^\d{5}(-?\d{4})?$/, "Invalid zip code format");

export const routingSchema = z
  .string()
  .length(9, "Routing number must be exactly 9 digits")
  .regex(/^\d{9}$/, "Routing number must contain only digits");

export const mccSchema = z
  .string()
  .length(4, "MCC code must be exactly 4 digits")
  .regex(/^\d{4}$/, "MCC code must contain only digits")
  .refine(
    (mccCode) => isApprovedMCCCode(mccCode),
    (mccCode) => {
      const details = getMCCCodeDetails(mccCode);
      return {
        message: details
          ? `MCC code ${mccCode} (${details.description}) is not approved for merchant registration. Please contact support for approved business categories.`
          : `MCC code ${mccCode} is not approved for merchant registration. Only specific business categories are currently supported.`,
      };
    }
  );

export const dobSchema = z
  .string()
  .min(1, "Date of birth is required")
  .regex(/^\d{4}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])$/, "Date of birth must be in YYYYMMDD format");

export const tcDateSchema = z
  .string()
  .min(1, "Terms & Conditions acceptance date is required")
  .regex(/^\d{12}$/, "TC date must be in YYYYMMDDHHMM format");

export const ssnSchema = z
  .string()
  .min(1, "SSN is required")
  .regex(/^\d{9}$/, "SSN must be 9 digits")
  .transform((val) => val.replace(/\D/g, ""));

export const percentageSchema = z
  .number()
  .int("Ownership percentage must be a whole number")
  .min(0, "Ownership percentage cannot be negative")
  .max(100, "Ownership percentage cannot exceed 100%");