import { z } from "zod";
import { routingSchema } from "./validation-helpers.schema.js";

export const accountSchema = z.object({
  primary: z.number().int().min(0).max(1, "Primary must be 0 or 1"),
  currency: z.string().default("USD"),
  account: z.object({
    method: z
      .number()
      .int()
      .refine(
        (val) => [8, 9, 10, 11].includes(val),
        "Account method must be 8 (personal checking), 9 (personal savings), 10 (business checking), or 11 (business savings)"
      ),
    number: z.string().min(1, "Account number is required"),
    routing: routingSchema,
  }),
});

export const plaidAccountSchema = z.object({
  primary: z.literal(1),
  publicToken: z.string().min(1, "Public token is required"),
  accountToken: z.string().min(1, "Account token is required"),
  platform: z.literal("PLAID"),
});

export const accountsSchema = z.union([
  z.array(accountSchema).min(1, "At least one account is required"),
  z.array(plaidAccountSchema).min(1, "Plaid account information is required"),
]);