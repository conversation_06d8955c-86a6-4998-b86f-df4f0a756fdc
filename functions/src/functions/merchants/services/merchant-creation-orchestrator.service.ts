import { logger } from "../../../helpers/logger.js";
import { createMerchant } from "../../../service/payrix.service.js";
import { prepareMerchantDataForPlaid } from "./plaid-verification.service.js";
import type { OnboardingRequest } from "../schemas/onboarding.schema.js";
import { setMerchantData } from "../../../service/merchant-data.js";
import type { MerchantData } from "../../../types/merchant-data.types.js";

export interface MerchantCreationResult {
  success: boolean;
  merchantId?: string;
  merchantData?: { id: string; [key: string]: unknown };
  error?: string;
}

export async function createMerchantWithPreparation(data: OnboardingRequest, requestId: string): Promise<MerchantCreationResult> {
  try {
    const merchantData = prepareMerchantData(data, requestId);

    logger.info("Creating merchant in Payrix (direct integration)", { requestId });
    const payrixResponse = await createMerchant(merchantData);
    const payrixEntityId = payrixResponse.id;

    if (!payrixEntityId) {
      throw new Error("Payrix response did not contain entity ID");
    }

    // Log the full response structure for debugging
    logger.info("Payrix response structure", {
      requestId,
      entityId: payrixEntityId,
      hasMerchantField: !!payrixResponse.merchant,
      merchantId: payrixResponse.merchant?.id,
      responseKeys: Object.keys(payrixResponse),
    });

    // Extract merchant ID from the response - it should be in the merchant.id field
    const merchantId = payrixResponse.merchant?.id || payrixEntityId;

    if (!merchantId) {
      throw new Error("Could not extract merchant ID from Payrix response");
    }

    logger.info("Payrix entity and merchant created successfully", {
      requestId,
      payrixEntityId,
      merchantId,
      merchantIdType: typeof merchantId,
      isString: typeof merchantId === "string",
    });

    // Store compliance policies in DynamoDB using the merchant ID
    if (data.compliancePolicies) {
      const merchantDataRecord: MerchantData = {
        merchantId: merchantId, // Use merchant ID instead of entity ID
        compliancePolicies: data.compliancePolicies,
        metadata: {
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1,
          status: "active",
        },
      };

      await setMerchantData(merchantId, merchantDataRecord);
      logger.info("Merchant compliance policies stored in DynamoDB", {
        requestId,
        payrixEntityId,
        merchantId,
      });
    }

    logger.info("Payrix merchant created successfully (direct integration)", {
      requestId,
      payrixEntityId,
    });

    return {
      success: true,
      merchantId: merchantId, // Return the merchant ID for consistency
      merchantData: payrixResponse,
    };
  } catch (error) {
    logger.error("Merchant creation failed", {
      requestId,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined,
    });

    return {
      success: false,
      error: error instanceof Error ? error.message : "Merchant creation failed",
    };
  }
}

function prepareMerchantData(data: OnboardingRequest, requestId: string): OnboardingRequest {
  let merchantData = { ...data };

  if (data.bankVerification?.verificationMethod === "plaid" && data.bankVerification.plaidData) {
    merchantData = prepareMerchantDataForPlaid(merchantData, data.bankVerification.plaidData, requestId);
  }

  return merchantData;
}
