import { logger } from "../../../helpers/logger.js";
import { OnboardingRequest } from "../schemas/onboarding.schema.js";
import { createUserAccountWithErrorHandling } from "./user-account-orchestrator.service.js";
import { processVerification } from "./verification-orchestrator.service.js";
import { createMerchantWithPreparation } from "./merchant-creation-orchestrator.service.js";
import { buildSuccessResponse } from "../utils/response-builders.js";
import { handlePayrixError } from "../utils/error-handling.js";

import type { APIGatewayProxyResult } from "aws-lambda";

export interface OnboardingResult {
  success: boolean;
  response?: APIGatewayProxyResult;
  error?: string;
}

export async function orchestrateOnboarding(data: OnboardingRequest, requestId: string): Promise<OnboardingResult> {
  logger.info("Processing direct Payrix submission", {
    requestId,
    email: data.email,
    legalName: data.name,
    clientIp: data.clientIp,
  });

  try {
    const merchantResult = await createMerchantWithPreparation(data, requestId);

    if (!merchantResult.success || !merchantResult.merchantId || !merchantResult.merchantData) {
      throw new Error(merchantResult.error || "Merchant creation failed");
    }

    const payrixEntityId = merchantResult.merchantData?.id; // Use entity ID from merchantData
    const payrixMerchantId = merchantResult.merchantId; // This is the merchant ID
    const payrixResponse = merchantResult.merchantData;

    if (!payrixEntityId) {
      throw new Error("Could not extract entity ID from merchant creation response");
    }

    logger.info("Using correct entity ID for bank verification", {
      requestId,
      payrixEntityId,
      payrixMerchantId,
      entityIdType: typeof payrixEntityId,
    });

    const userAccountResult = await createUserAccountWithErrorHandling(data, payrixMerchantId, requestId);

    const verificationResult = await processVerification(data, payrixEntityId, requestId, userAccountResult?.data?.id);

    return {
      success: true,
      response: buildSuccessResponse(
        data,
        payrixResponse,
        payrixEntityId,
        userAccountResult.success ? userAccountResult.data || null : null,
        verificationResult
      ),
    };
  } catch (payrixError) {
    return {
      success: false,
      response: handlePayrixError(payrixError as Error, requestId),
    };
  }
}
