import { createNote } from "../../../service/payrix.service.js";
import { logger } from "../../../helpers/logger.js";
import { VerificationUploadResult } from "./bank-verification.service.js";

export interface PlaidData {
  publicToken: string;
  accountToken: string;
  institutionName?: string;
  accountMask?: string;
}

export async function processPlaidVerification(
  entityId: string,
  plaidData: PlaidData | undefined,
  requestId: string,
  userAccountId?: string
): Promise<VerificationUploadResult> {
  logger.info("Processing Plaid bank verification", {
    requestId,
    payrixEntityId: entityId,
    hasPlaidData: !!plaidData,
    institutionName: plaidData?.institutionName,
    accountMask: plaidData?.accountMask,
  });

  if (!plaidData) {
    logger.warn("Plaid verification selected but no Plaid data provided", {
      requestId,
      payrixEntityId: entityId,
    });

    return {
      success: false,
      error: "Plaid verification data is missing",
    };
  }

  try {
    const noteData = {
      entity: entityId,
      note: `Plaid Bank Account Verification - ${plaidData.institutionName || "Bank"} account ending in ${
        plaidData.accountMask || "****"
      } verified via Plaid Link`,
      type: "plaid_verification",
      ...(userAccountId && { login: userAccountId }),
    };

    const noteResponse = await createNote(noteData);

    logger.info("Plaid verification note created successfully", {
      requestId,
      payrixEntityId: entityId,
      noteId: noteResponse.id,
      institutionName: plaidData.institutionName,
      accountMask: plaidData.accountMask,
    });

    return {
      success: true,
      noteId: noteResponse.id,
      documentId: undefined,
    };
  } catch (error) {
    logger.error("Failed to create Plaid verification note", {
      requestId,
      payrixEntityId: entityId,
      error,
    });

    return {
      success: false,
      error: "Failed to record Plaid verification",
    };
  }
}

import type { OnboardingRequest } from "../schemas/onboarding.schema.js";

export function prepareMerchantDataForPlaid(merchantData: OnboardingRequest, plaidData: PlaidData, requestId: string): OnboardingRequest {
  const updatedData = { ...merchantData };

  const plaidAccount = {
    primary: 1,
    publicToken: plaidData.publicToken,
    accountToken: plaidData.accountToken,
    platform: "PLAID" as const,
  };

  updatedData.accounts = [plaidAccount] as unknown as typeof updatedData.accounts;

  logger.info("Updated merchant data for Plaid verification", {
    requestId,
    hasPublicToken: !!plaidData.publicToken,
    hasAccountToken: !!plaidData.accountToken,
    institutionName: plaidData.institutionName,
  });

  return updatedData;
}
