import { AxiosError } from "axios";
import { createNote, createNoteDocument } from "../../../service/payrix.service.js";
import { logger } from "../../../helpers/logger.js";
import { validateVerificationFile } from "./file-validation.service.js";

export interface VerificationFile {
  name: string;
  size: number;
  type: string;
  content: string;
}

export interface VerificationUploadResult {
  success: boolean;
  noteId?: string;
  documentId?: string;
  error?: string;
}

export async function processBankVerification(
  entityId: string,
  verificationFile: VerificationFile,
  requestId: string,
  verificationMethod: string,
  loginId?: string
): Promise<VerificationUploadResult> {
  if (verificationMethod !== "manual") {
    return {
      success: true,
      noteId: undefined,
      documentId: undefined,
    };
  }

  if (!verificationFile) {
    throw new Error("Manual verification requires a file upload");
  }

  validateVerificationFile(verificationFile, requestId, entityId);

  try {
    const noteId = await createVerificationNote(entityId, loginId, requestId);
    const documentId = await uploadVerificationDocument(noteId, verificationFile, requestId, entityId);

    return {
      success: true,
      noteId,
      documentId,
    };
  } catch (error) {
    handleVerificationError(error as AxiosError, requestId, entityId);
    throw error;
  }
}

async function createVerificationNote(entityId: string, loginId: string | undefined, requestId: string): Promise<string> {

  const noteData = {
    entity: entityId,
    note: `Bank Account Verification Document - Voided Check uploaded during merchant onboarding for entity ${entityId}`,
    type: "note",
    ...(loginId && { login: loginId }),
  };

  const noteResponse = await createNote(noteData);

  if (!noteResponse?.id) {
    logger.error("Failed to create verification note - no ID returned from Payrix", {
      requestId,
      entityId,
      noteResponse,
    });
    throw new Error("Failed to create verification note - Payrix did not return note ID");
  }

  return noteResponse.id;
}

async function uploadVerificationDocument(noteId: string, verificationFile: VerificationFile, requestId: string, entityId: string): Promise<string> {
  const fileBuffer = Buffer.from(verificationFile.content, "base64");

  const documentData = {
    note: noteId,
    file: {
      filename: verificationFile.name,
      content: fileBuffer,
      contentType: verificationFile.type,
    },
    description: `Voided check for bank account verification - ${verificationFile.name}`,
  };

  const documentResponse = await createNoteDocument(documentData);

  if (!documentResponse?.id) {
    logger.error("Failed to create note document - no ID returned from Payrix", {
      requestId,
      entityId,
      noteId,
      documentResponse,
    });
    throw new Error("Failed to create note document - Payrix did not return document ID");
  }

  return documentResponse.id;
}

function handleVerificationError(error: AxiosError, requestId: string, entityId: string): void {
  if (error.response?.status === 400) {
    logger.error("Bad request to Payrix API for bank verification", {
      requestId,
      entityId,
      error: error.response.data,
    });
    throw new Error("Invalid data provided for bank verification upload");
  } else if (error.response?.status === 401) {
    logger.error("Authentication failed with Payrix API for bank verification", {
      requestId,
      entityId,
    });
    throw new Error("Authentication failed with Payrix API");
  } else if (error.response?.status === 404) {
    logger.error("Entity not found in Payrix for bank verification", {
      requestId,
      entityId,
    });
    throw new Error(`Entity ${entityId} not found in Payrix system`);
  }

  logger.error("Priority bank verification upload failed", {
    requestId,
    entityId,
    error,
  });
}
