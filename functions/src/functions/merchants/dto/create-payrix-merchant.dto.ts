export interface CreatePayrixMerchantDto {
  name: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone: string;
  email: string;
  ein: string;
  website: string;
  type: number;
  public: number;
  status: number;
  tcVersion: string;
  tcDate: string;
  clientIp?: string;
  currency: string;

  accounts: (
    | {
        primary: number;
        currency?: string;
        account: {
          method: number;
          number: string;
          routing: string;
        };
      }
    | {
        primary: number;
        publicToken: string;
        accountToken: string;
        platform: "PLAID";
      }
  )[];

  merchant: {
    dba: string;
    new: number;
    mcc: string;
    status: string;
    annualCCSales?: number;
    avgTicket?: number;
    established?: string;
    members: {
      title: string;
      first: string;
      last: string;
      ssn?: string;
      dob: string;
      dl?: string;
      dlstate?: string;
      ownership: number;
      significantResponsibility: number;
      politicallyExposed: number;
      email: string;
      phone: string;
      primary: string;
      address1: string;
      address2?: string;
      city: string;
      state: string;
      zip: string;
      country: string;
    }[];
  };
}