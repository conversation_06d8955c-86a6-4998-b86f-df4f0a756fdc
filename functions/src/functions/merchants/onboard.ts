import { APIGatewayProxyHandler } from "aws-lambda";
import { validateOnboardingRequest } from "./schemas/onboarding.schema.js";
import { createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { handleValidationError } from "./utils/error-handling.js";
import { orchestrateOnboarding } from "./services/onboarding.orchestrator.js";

export const handler: APIGatewayProxyHandler = async (event) => {
  const requestId = event.requestContext.requestId;

  try {
    const body = JSON.parse(event.body || "{}");
    const validation = validateOnboardingRequest(body);

    if (!validation.success || !validation.data) {
      return handleValidationError(validation.errors || ["Unknown validation error"], requestId);
    }

    const data = validation.data;

    if (!data.clientIp) {
      return handleValidationError(["Client IP is required"], requestId);
    }

    const result = await orchestrateOnboarding(data, requestId);

    if (!result.success) {
      return result.response || createErrorResponse(500, "Internal Server Error", "Onboarding failed");
    }

    return result.response || createErrorResponse(500, "Internal Server Error", "Onboarding completed but no response generated");
  } catch (error) {
    const err = error as Error;
    logger.error("Error in onboard handler", {
      requestId,
      error: err.message,
      stack: err.stack,
    });

    return createErrorResponse(500, "Internal Server Error", err.message);
  }
};
