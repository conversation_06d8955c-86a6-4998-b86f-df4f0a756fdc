import { APIGatewayProxyHandler } from "aws-lambda";
import { createErrorResponse } from "../../helpers/response.js";
import { logger } from "../../helpers/logger.js";
import { z } from "zod";

const ProcessPlaidAccountRequestSchema = z.object({
  publicToken: z.string().min(1, "Public token is required"),
  accountId: z.string().min(1, "Account ID is required"),
  metadata: z
    .object({
      institution: z.object({
        name: z.string(),
        institution_id: z.string(),
      }),
      account: z.object({
        id: z.string(),
        name: z.string(),
        mask: z.string(),
        type: z.string(),
        subtype: z.string(),
        verification_status: z.string().nullable().optional(),
      }),
      link_session_id: z.string(),
    })
    .optional(),
});

export const handler: APIGatewayProxyHandler = async (event) => {
  const requestId = event.requestContext.requestId;

  try {
    if (!event.body) {
      return createErrorResponse(400, "Request body is required", requestId);
    }

    let requestData;
    try {
      requestData = JSON.parse(event.body);
    } catch {
      return createErrorResponse(400, "Invalid JSON in request body", requestId);
    }

    const validationResult = ProcessPlaidAccountRequestSchema.safeParse(requestData);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map((err) => `${err.path.join(".")}: ${err.message}`);
      return createErrorResponse(400, `Validation failed: ${errors.join(", ")}`, requestId);
    }

    const { publicToken, accountId, metadata } = validationResult.data;

    if (!publicToken.startsWith("public-sandbox-") && !publicToken.startsWith("public-production-")) {
      return createErrorResponse(400, "Invalid public token format", requestId);
    }


    return {
      statusCode: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Content-Type",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
      },
      body: JSON.stringify({
        success: true,
        message: "Plaid account processed successfully",
        data: {
          publicToken,
          accountToken: accountId,
          platform: "PLAID",
          institutionName: metadata?.institution?.name,
          accountName: metadata?.account?.name,
          accountMask: metadata?.account?.mask,
          accountType: metadata?.account?.type,
          accountSubtype: metadata?.account?.subtype,
          verificationStatus: metadata?.account?.verification_status,
          processedAt: new Date().toISOString(),
        },
      }),
    };
  } catch (error) {
    logger.error("Error processing Plaid account", { requestId, error });

    // Handle specific errors
    if (error instanceof Error) {
      if (error.message.includes("expired") || error.message.includes("invalid token")) {
        return createErrorResponse(400, "Plaid token has expired or is invalid. Please restart the verification process.", requestId);
      } else if (error.message.includes("account not found")) {
        return createErrorResponse(404, "Plaid account not found or inaccessible", requestId);
      }
    }

    return createErrorResponse(500, "Internal server error", requestId);
  }
};
