import { checkMerchantExists } from "../../../service/payrix.service.js";
import { logger } from "../../../helpers/logger.js";
import { createResponse } from "../../../helpers/response.js";
import type { APIGatewayProxyResult } from "aws-lambda";

export interface MerchantValidationResult {
  exists: boolean;
  response?: APIGatewayProxyResult;
}

export async function checkExistingMerchant(email: string, ein: string | undefined, requestId: string): Promise<MerchantValidationResult> {
  try {
    logger.info("Checking for existing merchant in Payrix", {
      requestId,
      email,
    });

    const merchantExists = await checkMerchantExists(email, ein);

    if (merchantExists) {
      logger.warn("Merchant already exists in Payrix", {
        requestId,
        email,
        ein: ein ? "[REDACTED]" : undefined,
      });

      return {
        exists: true,
        response: createResponse(409, {
          success: false,
          message:
            "A merchant with this email address already exists in our system. Please use a different email address or contact support if you believe this is an error.",
          error: "MERCHANT_ALREADY_EXISTS",
          details: {
            email,
            conflictType: "duplicate_merchant",
          },
        }),
      };
    }

    logger.info("No existing merchant found, proceeding with onboarding", {
      requestId,
      email,
    });

    return { exists: false };
  } catch (duplicateCheckError) {
    logger.error("Error during duplicate merchant check", {
      requestId,
      error: (duplicateCheckError as Error).message,
      stack: (duplicateCheckError as Error).stack,
    });

    return {
      exists: false,
      response: createResponse(500, {
        success: false,
        message: "Unable to verify merchant uniqueness. Please try again later.",
        error: "DUPLICATE_CHECK_FAILED",
        details: {
          reason: "Service temporarily unavailable",
        },
      }),
    };
  }
}
