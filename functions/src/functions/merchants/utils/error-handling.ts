import { createResponse } from "../../../helpers/response.js";
import { logger } from "../../../helpers/logger.js";
import type { APIGatewayProxyResult } from "aws-lambda";

export function handlePayrixError(error: Error, requestId: string, payrixEntityId?: string): APIGatewayProxyResult {
  logger.error("Payrix API error (direct integration)", {
    requestId,
    error: error.message,
    stack: error.stack,
  });

  return createResponse(422, {
    success: false,
    message: "Payrix onboarding failed. Please check your information and try again.",
    error: error.message,
    ...(payrixEntityId && { payrixEntityId }),
  });
}

export function handleValidationError(errors: string[], requestId: string): APIGatewayProxyResult {
  logger.warn("Onboarding validation failed", {
    requestId,
    errors,
  });

  return createResponse(400, {
    success: false,
    message: "Validation failed",
    errors,
  });
}
