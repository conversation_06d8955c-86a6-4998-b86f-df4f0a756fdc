import { createResponse } from "../../../helpers/response.js";
import type { APIGatewayProxyResult } from "aws-lambda";
import type { OnboardingRequest } from "../schemas/onboarding.schema.js";

interface PayrixResponse {
  id: string;
  [key: string]: unknown;
}

interface UserAccountData {
  id: string;
  email: string;
  sanitizedUsername?: string;
  originalUsername?: string;
  [key: string]: unknown;
}

interface VerificationUploadResult {
  success: boolean;
  noteId?: string;
  documentId?: string;
  error?: string;
}

export function buildSuccessResponse(
  data: OnboardingRequest,
  payrixResponse: PayrixResponse,
  payrixEntityId: string,
  userAccountData: UserAccountData | null,
  verificationUploadResult: VerificationUploadResult | null = null
): APIGatewayProxyResult {
  let message = "Merchant onboarding completed successfully via direct Payrix integration";
  if (verificationUploadResult?.success && userAccountData) {
    message += " - Bank verification uploaded and user account created";
  } else if (verificationUploadResult?.success && !userAccountData) {
    message += " - Bank verification uploaded successfully (user account creation failed)";
  } else if (!verificationUploadResult?.success && userAccountData) {
    message += " - User account created (bank verification upload failed)";
  } else if (!verificationUploadResult?.success && !userAccountData) {
    message += " - Merchant created (bank verification and user account creation failed)";
  }

  return createResponse(201, {
    success: true,
    payrixEntityId,
    message,
    data: {
      merchant: {
        legal_name: data.name,
        email: data.email,
        verification_status: 1,
      },
      payrixResponse,
      userAccount: userAccountData
        ? {
            id: userAccountData.id,
            username: userAccountData.sanitizedUsername || data.userAccount?.username || "unknown",
            originalUsername: data.userAccount?.username || "unknown",
            email: userAccountData.email,
            created: true,
            status: "success",
          }
        : {
            created: false,
            status: "failed",
            error: "User account creation failed",
          },
      bankVerification: verificationUploadResult
        ? {
            processed: true,
            uploaded: verificationUploadResult.success,
            status: verificationUploadResult.success ? "success" : "failed",
            noteId: verificationUploadResult.noteId,
            documentId: verificationUploadResult.documentId,
            error: verificationUploadResult.error,
          }
        : {
            processed: false,
            uploaded: false,
            status: "not_required",
            message: "No manual verification file provided",
          },
    },
  });
}
