import { AxiosError } from "axios";
import FormData from "form-data";
import { logger } from "../../helpers/logger.js";
import { createPayrixApiClient } from "./api-client.js";

const apiClient = createPayrixApiClient();

export async function createNote(noteData: { entity: string; note: string; type?: string; login?: string }): Promise<{ id: string }> {
  try {
    logger.info("Creating note in Payrix", {
      entity: noteData.entity,
      type: noteData.type,
      noteLength: noteData.note.length,
    });

    const response = await apiClient.post("/notes", noteData);

    logger.info("Payrix note creation response", {
      status: response.status,
      data: response.data,
    });

    // Handle multiple possible response structures like payment service
    let noteResponse;
    if (response.data?.response?.data?.[0]) {
      noteResponse = response.data.response.data[0];
    } else if (response.data?.data?.[0]) {
      noteResponse = response.data.data[0];
    } else if (response.data?.id) {
      noteResponse = response.data;
    }

    if (!noteResponse?.id) {
      logger.error("Invalid Payrix response structure for note creation", {
        responseData: response.data,
        status: response.status,
        noteData: noteData,
      });
      throw new Error("Invalid Payrix response structure: no note ID found");
    }

    logger.info("Note created successfully", {
      noteId: noteResponse.id,
      entity: noteData.entity,
    });

    return { id: noteResponse.id };
  } catch (error) {
    logger.error("Error creating note in Payrix", { error });
    if (error instanceof AxiosError) {
      logger.error("Payrix API error details", {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
      });
    }
    throw error;
  }
}

export async function createNoteDocument(documentData: {
  note: string;
  file: { filename: string; content: Buffer; contentType: string };
  description?: string;
}): Promise<{ id: string }> {
  try {
    const noteDocumentId = await createNoteDocumentMetadata(documentData);
    await uploadFileToNoteDocument(noteDocumentId, documentData);

    return { id: noteDocumentId };
  } catch (error) {
    handleNoteDocumentError(error as AxiosError, documentData);
    throw error;
  }
}

async function createNoteDocumentMetadata(documentData: {
  note: string;
  file: { filename: string; content: Buffer; contentType: string };
  description?: string;
}): Promise<string> {
  const fileExtension = documentData.file.filename.split(".").pop()?.toLowerCase() || "png";

  const noteDocumentPayload = {
    note: documentData.note,
    type: fileExtension,
    documentType: "voidCheck",
    description: documentData.description || "Void check for bank account verification",
    name: documentData.file.filename,
  };

  logger.info("Creating note document metadata", {
    noteId: documentData.note,
    payload: noteDocumentPayload,
  });

  const noteDocResponse = await apiClient.post("/noteDocuments", noteDocumentPayload);

  logger.info("Note document metadata response", {
    status: noteDocResponse.status,
    data: noteDocResponse.data,
  });

  // Handle multiple possible response structures
  let noteDocumentData;
  if (noteDocResponse.data?.response?.data?.[0]) {
    noteDocumentData = noteDocResponse.data.response.data[0];
  } else if (noteDocResponse.data?.data?.[0]) {
    noteDocumentData = noteDocResponse.data.data[0];
  } else if (noteDocResponse.data?.id) {
    noteDocumentData = noteDocResponse.data;
  }

  if (!noteDocumentData?.id) {
    logger.error("Invalid Payrix response structure for note document creation", {
      responseData: noteDocResponse.data,
      status: noteDocResponse.status,
      payload: noteDocumentPayload,
    });
    throw new Error("Failed to create note document metadata - no ID returned");
  }

  return noteDocumentData.id;
}

async function uploadFileToNoteDocument(
  noteDocumentId: string,
  documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }
): Promise<void> {
  const formData = new FormData();

  formData.append("file", documentData.file.content, {
    filename: documentData.file.filename,
    contentType: documentData.file.contentType,
  });

  const uploadPayload = {
    integration: "WORLDPAY",
    direction: "upload",
    name: documentData.file.filename,
    description: documentData.description || "Void Check for Bank Account Verification",
  };

  formData.append("json", JSON.stringify(uploadPayload));

  await apiClient.post(`/files/noteDocuments/${noteDocumentId}`, formData, {
    headers: {
      ...formData.getHeaders(),
    },
  });
}

function handleNoteDocumentError(
  error: AxiosError,
  documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }
): void {
  logger.error("Error in createNoteDocument 3-step process", {
    error,
    noteId: documentData.note,
    fileName: documentData.file.filename,
  });

  if (error instanceof AxiosError) {
    logger.error("Payrix API error details", {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
    });

    const status = error.response?.status;
    const payrixData = error.response?.data as Record<string, unknown> | undefined;

    if (status === 400) {
      throw new Error(`Payrix validation error: ${(payrixData?.message as string) || "Invalid request data"}`);
    } else if (status === 401 || status === 403) {
      throw new Error("Payrix authentication failed. Please check API credentials.");
    } else if (status === 404) {
      throw new Error("Note not found. Please ensure the note exists before uploading documents.");
    } else if (status === 413) {
      throw new Error("File too large for Payrix. Please use a smaller file.");
    } else if (status === 422) {
      throw new Error(`Payrix processing error: ${(payrixData?.message as string) || "Unable to process request"}`);
    } else if (status && status >= 500) {
      throw new Error("Payrix service temporarily unavailable. Please try again later.");
    }
  }
}
