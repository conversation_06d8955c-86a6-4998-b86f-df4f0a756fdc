import type { OnboardingRequest } from "../../functions/merchants/schemas/onboarding.schema.js";
import type {
  PayrixMerchantResponse,
  PayrixUserResponse,
  TokenPaymentData,
  UserAccountData,
  PaymentResult,
  MerchantValidationResult,
  TokenDeletionResult,
} from "../../types/payrix.types.js";

export interface IPayrixMerchantValidationService {
  checkMerchantExists(email: string, ein?: string): Promise<boolean>;
  validateMerchantById(merchantId: string): Promise<MerchantValidationResult>;
}

export interface IPayrixMerchantCreationService {
  createMerchant(merchantData: OnboardingRequest): Promise<PayrixMerchantResponse>;
  createPlaidLinkToken(linkTokenData: {
    userId: string;
    countryCode: string;
    redirectUri: string;
  }): Promise<{ linkToken: string; requestId: string }>;
}

export interface IPayrixNoteManagementService {
  createNote(noteData: { entity: string; note: string; type?: string; login?: string }): Promise<{ id: string }>;
  createNoteDocument(documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }): Promise<{ id: string }>;
}

export interface IPayrixUserService {
  createUserAccount(userData: UserAccountData): Promise<PayrixUserResponse>;
}

export interface IPayrixPaymentService {
  processTokenPayment(paymentData: TokenPaymentData): Promise<PaymentResult>;
  deleteToken(token: string): Promise<TokenDeletionResult>;
  cleanupToken(token: string): Promise<TokenDeletionResult>;
}

export interface IPayrixMerchantService {
  checkMerchantExists(email: string, ein?: string): Promise<boolean>;
  validateMerchantById(merchantId: string): Promise<MerchantValidationResult>;
  createMerchant(merchantData: OnboardingRequest): Promise<PayrixMerchantResponse>;
  createNote(noteData: { entity: string; note: string; type?: string; login?: string }): Promise<{ id: string }>;
  createNoteDocument(documentData: {
    note: string;
    file: { filename: string; content: Buffer; contentType: string };
    description?: string;
  }): Promise<{ id: string }>;
  createPlaidLinkToken(linkTokenData: {
    userId: string;
    countryCode: string;
    redirectUri: string;
  }): Promise<{ linkToken: string; requestId: string }>;
}

export interface IServiceContainer {
  getMerchantValidationService(): IPayrixMerchantValidationService;
  getMerchantCreationService(): IPayrixMerchantCreationService;
  getNoteManagementService(): IPayrixNoteManagementService;
  getUserService(): IPayrixUserService;
  getPaymentService(): IPayrixPaymentService;
  getMerchantService(): IPayrixMerchantService;
}
