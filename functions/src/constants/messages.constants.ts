export const ERROR_MESSAGES = {
  BODY_REQUIRED: "Request body is required",
  BODY_REQUIRED_DETAIL: "Please provide required parameters",
  VALIDATION_FAILED: "Validation failed",
  INVALID_REQUEST: "Invalid request data",
  INVALID_JSON: "Request body must be valid JSON",
  
  INVALID_TOKEN_FORMAT: "Invalid token format",
  TOKEN_VALIDATION_FAILED: "Token validation failed",
  INVALID_OR_EXPIRED_TOKEN: "Invalid or expired token",
  TOKEN_GENERATION_FAILED: "Failed to generate integration token",
  TOKEN_NOT_FOUND: "Token not found",
  TOKEN_ALREADY_USED: "Token already used",
  TOKEN_EXPIRED: "Token expired",
  
  MERCHANT_VALIDATION_FAILED: "Merchant validation failed",
  INVALID_OR_INACTIVE_MERCHANT: "Invalid or inactive merchant",
  MISSING_COMPLIANCE_POLICIES: "Merchant compliance policies not found. Please complete merchant onboarding first.",
  INVALID_COMPLIANCE_POLICIES: "Merchant compliance policies are incomplete or invalid.",
  
  MISSING_REQUIRED_FIELD: "Missing required field",
  INVALID_AMOUNT: "Invalid amount",
  INVALID_DESCRIPTION: "Invalid description",
  INVALID_RETURN_URL: "Invalid return URL",
  INVALID_TAX_AMOUNT: "Tax amount must be between 0.1% and 30% of the total amount",
  ITEMIZED_TRANSACTION_REQUIREMENTS: "Order number, invoice number, or customer code is required for itemized transactions",
  
  INTERNAL_ERROR: "Internal server error",
  UNKNOWN_ERROR: "Unknown error",
  PROCESSING_FAILED: "Processing failed",
} as const;

export const SUCCESS_MESSAGES = {
  TOKEN_GENERATED: "Integration token generated successfully",
  TOKEN_VALIDATED: "Token validated and payment configuration generated successfully",
  TOKEN_STATUS_RETRIEVED: "Token status retrieved successfully",
  PAYMENT_PROCESSED: "Payment processed successfully",
  MERCHANT_ONBOARDED: "Merchant onboarded successfully",
  POLICIES_UPDATED: "Compliance policies updated successfully",
} as const;

export const DETAIL_MESSAGES = {
  PROVIDE_TOKEN: "Please provide a token",
  PROVIDE_MERCHANT_ID: "Please provide merchantId and description",
  COMPLIANCE_REQUIRED: "Compliance policies must be configured during merchant onboarding before payment processing can begin.",
} as const;