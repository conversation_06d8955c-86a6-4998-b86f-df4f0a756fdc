export const VALIDATION_LIMITS = {
  MIN_AMOUNT: 50,
  MAX_AMOUNT: 10000000,
  TOKEN_LENGTH_INTEGRATION: 64, // Integration tokens (backend generated)
  TOKEN_LENGTH_PAYMENT: 32, // Payrix payment tokens (PayFields generated)
  MIN_DESCRIPTION_LENGTH: 1,
  MAX_DESCRIPTION_LENGTH: 500,
  MAX_TAX_PERCENTAGE: 30,
  MIN_TAX_PERCENTAGE: 0.1,
} as const;

export const DEFAULTS = {
  AMOUNT: 1000,
  EXPIRES_IN_MINUTES: 60,
  CURRENCY: "USD",
  COUNTRY: "USA",
  MODE: "token" as const,
  TXN_TYPE: "auth" as const,
  PUBLIC_KEY: "default-public-key",
  MERCHANT_NAME: "Unknown",
} as const;

export const TIME_CONVERSION = {
  MINUTES_TO_MS: 60 * 1000,
  HOURS_TO_MS: 60 * 60 * 1000,
  SECONDS_TO_MS: 1000,
} as const;

export const TOKEN_STATUS = {
  VALID: "valid" as const,
  EXPIRED: "expired" as const,
  USED: "used" as const,
  INVALID: "invalid" as const,
} as const;

export const PAYMENT_METHODS = {
  CARD: "card" as const,
  GOOGLE_PAY: "google_pay" as const,
} as const;
