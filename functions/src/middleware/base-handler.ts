import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { z, ZodSchema } from "zod";
import { createIframeResponse, createResponse } from "../helpers/response.js";
import { logger } from "../helpers/logger.js";
import { withIframeSecurity } from "./security.js";
import { HTTP_STATUS } from "../constants/http.constants.js";
import { ERROR_MESSAGES } from "../constants/messages.constants.js";

export interface HandlerOptions {
  useIframeResponse?: boolean;
  withSecurity?: boolean;
  schema?: ZodSchema;
  errorMessage?: string;
  successMessage?: string;
}

export interface HandlerContext<TBody = unknown> {
  event: APIGatewayProxyEvent;
  body: TBody;
  params: Record<string, string | undefined>;
  headers: Record<string, string | undefined>;
}

export type HandlerFunction<TResult = unknown, TBody = unknown> = (context: HandlerContext<TBody>) => Promise<TResult>;

const parseRequestBody = (event: APIGatewayProxyEvent, schema?: ZodSchema): unknown => {
  if (!event.body) {
    return null;
  }

  try {
    const parsed = JSON.parse(event.body);
    if (schema) {
      return schema.parse(parsed);
    }
    return parsed;
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError(ERROR_MESSAGES.VALIDATION_FAILED, error.errors);
    }
    throw new ValidationError(ERROR_MESSAGES.INVALID_JSON);
  }
};

export class ValidationError extends Error {
  constructor(message: string, public details?: unknown, public statusCode: number = HTTP_STATUS.BAD_REQUEST) {
    super(message);
    this.name = "ValidationError";
  }
}

export class BusinessError extends Error {
  constructor(message: string, public statusCode: number = HTTP_STATUS.BAD_REQUEST, public errorCode?: string, public details?: unknown) {
    super(message);
    this.name = "BusinessError";
  }
}

const handleError = (error: unknown, options?: HandlerOptions): APIGatewayProxyResult => {
  const createResponseFn = options?.useIframeResponse ? createIframeResponse : createResponse;

  if (error instanceof ValidationError) {
    return createResponseFn(error.statusCode, {
      error: ERROR_MESSAGES.VALIDATION_FAILED,
      message: error.message,
      details: error.details,
    });
  }

  if (error instanceof BusinessError) {
    return createResponseFn(error.statusCode, {
      error: error.errorCode || ERROR_MESSAGES.INTERNAL_ERROR,
      message: error.message,
      details: error.details,
    });
  }

  logger.error("Unhandled error in handler", { error });

  return createResponseFn(HTTP_STATUS.INTERNAL_ERROR, {
    error: ERROR_MESSAGES.INTERNAL_ERROR,
    message: options?.errorMessage || ERROR_MESSAGES.PROCESSING_FAILED,
    details: error instanceof Error ? error.message : ERROR_MESSAGES.UNKNOWN_ERROR,
  });
};

export const createHandler = <TResult = unknown, TBody = unknown>(handlerFn: HandlerFunction<TResult, TBody>, options?: HandlerOptions) => {
  const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    try {
      const body = options?.schema ? parseRequestBody(event, options.schema) : event.body ? parseRequestBody(event) : null;

      const context: HandlerContext<TBody> = {
        event,
        body: body as TBody,
        params: event.pathParameters || {},
        headers: event.headers || {},
      };

      const result = await handlerFn(context);

      const createResponseFn = options?.useIframeResponse ? createIframeResponse : createResponse;

      return createResponseFn(HTTP_STATUS.OK, {
        success: true,
        message: options?.successMessage || "Request processed successfully",
        data: result,
      });
    } catch (error) {
      return handleError(error, options);
    }
  };

  return options?.withSecurity !== false && options?.useIframeResponse ? withIframeSecurity(handler) : handler;
};

export const createIframeHandler = <TResult = unknown, TBody = unknown>(
  handlerFn: HandlerFunction<TResult, TBody>,
  options?: Omit<HandlerOptions, "useIframeResponse" | "withSecurity">
) => {
  return createHandler(handlerFn, {
    ...options,
    useIframeResponse: true,
    withSecurity: true,
  });
};
