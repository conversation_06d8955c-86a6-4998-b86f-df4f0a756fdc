import type { APIGatewayProxyResult } from "aws-lambda";
import { CORS_HEADERS } from "../constants/http.constants.js";

export const createResponse = (statusCode: number, data: object): APIGatewayProxyResult => ({
  statusCode,
  headers: CORS_HEADERS.DEFAULT,
  body: JSON.stringify(data),
});

export const createIframeResponse = (statusCode: number, data: object): APIGatewayProxyResult => ({
  statusCode,
  headers: CORS_HEADERS.IFRAME,
  body: JSON.stringify(data),
});

export const createSuccessResponse = (data: object) => createResponse(200, data);

export const createErrorResponse = (statusCode: number, error: string, message?: string) =>
  createResponse(statusCode, { error, ...(message && { message }) });
